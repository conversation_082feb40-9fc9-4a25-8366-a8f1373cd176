<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Menu">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="@android:style/TextAppearance.Material.Widget.Button.Inverse">
    </style>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V23.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V23.Theme.AppCompat.Light">
    </style>
    <style name="Base.V23.Theme.AppCompat" parent="@style/Base.V22.Theme.AppCompat">
        <item name="actionBarItemBackground">?android:attr/actionBarItemBackground</item>
        <item name="actionMenuTextAppearance">?android:attr/actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:attr/actionMenuTextColor</item>
        <item name="controlBackground">@drawable/abc_control_background_material</item>
        <item name="ratingBarStyleIndicator">?android:attr/ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:attr/ratingBarStyleSmall</item>
    </style>
    <style name="Base.V23.Theme.AppCompat.Light" parent="@style/Base.V22.Theme.AppCompat.Light">
        <item name="actionBarItemBackground">?android:attr/actionBarItemBackground</item>
        <item name="actionMenuTextAppearance">?android:attr/actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:attr/actionMenuTextColor</item>
        <item name="controlBackground">@drawable/abc_control_background_material</item>
        <item name="ratingBarStyleIndicator">?android:attr/ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:attr/ratingBarStyleSmall</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@android:style/Widget.Material.Button.Borderless.Colored">
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored" parent="@android:style/Widget.Material.Button.Colored">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="@android:style/Widget.Material.RatingBar.Indicator">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="@android:style/Widget.Material.RatingBar.Small">
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@android:style/Widget.Material.Spinner.Underlined">
    </style>
    <style name="CardView" parent="@style/Base.CardView">
        <item name="cardBackgroundColor">?android:attr/colorBackgroundFloating</item>
    </style>
</resources>
