<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="actionBarDivider" format="reference">
    </attr>
    <attr name="actionBarItemBackground" format="reference">
    </attr>
    <attr name="actionBarPopupTheme" format="reference">
    </attr>
    <attr name="actionBarSize" format="dimension">
        <enum name="wrap_content" value="0" />
    </attr>
    <attr name="actionBarSplitStyle" format="reference">
    </attr>
    <attr name="actionBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabStyle" format="reference">
    </attr>
    <attr name="actionBarTabTextStyle" format="reference">
    </attr>
    <attr name="actionBarTheme" format="reference">
    </attr>
    <attr name="actionBarWidgetTheme" format="reference">
    </attr>
    <attr name="actionButtonStyle" format="reference">
    </attr>
    <attr name="actionDropDownStyle" format="reference">
    </attr>
    <attr name="actionLayout" format="reference">
    </attr>
    <attr name="actionMenuTextAppearance" format="reference">
    </attr>
    <attr name="actionMenuTextColor" format="reference|color">
    </attr>
    <attr name="actionModeBackground" format="reference">
    </attr>
    <attr name="actionModeCloseButtonStyle" format="reference">
    </attr>
    <attr name="actionModeCloseDrawable" format="reference">
    </attr>
    <attr name="actionModeCopyDrawable" format="reference">
    </attr>
    <attr name="actionModeCutDrawable" format="reference">
    </attr>
    <attr name="actionModeFindDrawable" format="reference">
    </attr>
    <attr name="actionModePasteDrawable" format="reference">
    </attr>
    <attr name="actionModePopupWindowStyle" format="reference">
    </attr>
    <attr name="actionModeSelectAllDrawable" format="reference">
    </attr>
    <attr name="actionModeShareDrawable" format="reference">
    </attr>
    <attr name="actionModeSplitBackground" format="reference">
    </attr>
    <attr name="actionModeStyle" format="reference">
    </attr>
    <attr name="actionModeWebSearchDrawable" format="reference">
    </attr>
    <attr name="actionOverflowButtonStyle" format="reference">
    </attr>
    <attr name="actionOverflowMenuStyle" format="reference">
    </attr>
    <attr name="actionProviderClass" format="string">
    </attr>
    <attr name="actionViewClass" format="string">
    </attr>
    <attr name="activityChooserViewStyle" format="reference">
    </attr>
    <attr name="alertDialogButtonGroupStyle" format="reference">
    </attr>
    <attr name="alertDialogCenterButtons" format="boolean">
    </attr>
    <attr name="alertDialogStyle" format="reference">
    </attr>
    <attr name="alertDialogTheme" format="reference">
    </attr>
    <attr name="allowStacking" format="boolean">
    </attr>
    <attr name="alpha" format="float">
    </attr>
    <attr name="alphabeticModifiers">
        <flag name="ALT" value="2" />
        <flag name="CTRL" value="4096" />
        <flag name="FUNCTION" value="8" />
        <flag name="META" value="65536" />
        <flag name="SHIFT" value="1" />
        <flag name="SYM" value="4" />
    </attr>
    <attr name="arrowHeadLength" format="dimension">
    </attr>
    <attr name="arrowShaftLength" format="dimension">
    </attr>
    <attr name="autoCompleteTextViewStyle" format="reference">
    </attr>
    <attr name="autoSizeMaxTextSize" format="dimension">
    </attr>
    <attr name="autoSizeMinTextSize" format="dimension">
    </attr>
    <attr name="autoSizePresetSizes" format="reference">
    </attr>
    <attr name="autoSizeStepGranularity" format="dimension">
    </attr>
    <attr name="autoSizeTextType">
        <enum name="none" value="0" />
        <enum name="uniform" value="1" />
    </attr>
    <attr name="background" format="reference">
    </attr>
    <attr name="backgroundSplit" format="reference|color">
    </attr>
    <attr name="backgroundStacked" format="reference|color">
    </attr>
    <attr name="backgroundTint" format="color">
    </attr>
    <attr name="backgroundTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="barLength" format="dimension">
    </attr>
    <attr name="behavior_autoHide" format="boolean">
    </attr>
    <attr name="behavior_fitToContents" format="boolean">
    </attr>
    <attr name="behavior_hideable" format="boolean">
    </attr>
    <attr name="behavior_overlapTop" format="dimension">
    </attr>
    <attr name="behavior_peekHeight" format="dimension">
        <enum name="auto" value="-1" />
    </attr>
    <attr name="behavior_skipCollapsed" format="boolean">
    </attr>
    <attr name="borderWidth" format="dimension">
    </attr>
    <attr name="borderlessButtonStyle" format="reference">
    </attr>
    <attr name="bottomAppBarStyle" format="reference">
    </attr>
    <attr name="bottomNavigationStyle" format="reference">
    </attr>
    <attr name="bottomSheetDialogTheme" format="reference">
    </attr>
    <attr name="bottomSheetStyle" format="reference">
    </attr>
    <attr name="boxBackgroundColor" format="color">
    </attr>
    <attr name="boxBackgroundMode">
        <enum name="filled" value="1" />
        <enum name="none" value="0" />
        <enum name="outline" value="2" />
    </attr>
    <attr name="boxCollapsedPaddingTop" format="dimension">
    </attr>
    <attr name="boxCornerRadiusBottomEnd" format="dimension">
    </attr>
    <attr name="boxCornerRadiusBottomStart" format="dimension">
    </attr>
    <attr name="boxCornerRadiusTopEnd" format="dimension">
    </attr>
    <attr name="boxCornerRadiusTopStart" format="dimension">
    </attr>
    <attr name="boxStrokeColor" format="color">
    </attr>
    <attr name="boxStrokeWidth" format="dimension">
    </attr>
    <attr name="buttonBarButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNegativeButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNeutralButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarPositiveButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarStyle" format="reference">
    </attr>
    <attr name="buttonGravity">
        <flag name="bottom" value="80" />
        <flag name="top" value="48" />
    </attr>
    <attr name="buttonIconDimen" format="dimension">
    </attr>
    <attr name="buttonPanelSideLayout" format="reference">
    </attr>
    <attr name="buttonStyle" format="reference">
    </attr>
    <attr name="buttonStyleSmall" format="reference">
    </attr>
    <attr name="buttonTint" format="color">
    </attr>
    <attr name="buttonTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="cardBackgroundColor" format="color">
    </attr>
    <attr name="cardCornerRadius" format="dimension">
    </attr>
    <attr name="cardElevation" format="dimension">
    </attr>
    <attr name="cardMaxElevation" format="dimension">
    </attr>
    <attr name="cardPreventCornerOverlap" format="boolean">
    </attr>
    <attr name="cardUseCompatPadding" format="boolean">
    </attr>
    <attr name="cardViewStyle" format="reference">
    </attr>
    <attr name="checkboxStyle" format="reference">
    </attr>
    <attr name="checkedChip" format="reference">
    </attr>
    <attr name="checkedIcon" format="reference">
    </attr>
    <attr name="checkedIconEnabled" format="boolean">
    </attr>
    <attr name="checkedIconVisible" format="boolean">
    </attr>
    <attr name="checkedTextViewStyle" format="reference">
    </attr>
    <attr name="chipBackgroundColor" format="color">
    </attr>
    <attr name="chipCornerRadius" format="dimension">
    </attr>
    <attr name="chipEndPadding" format="dimension">
    </attr>
    <attr name="chipGroupStyle" format="reference">
    </attr>
    <attr name="chipIcon" format="reference">
    </attr>
    <attr name="chipIconEnabled" format="boolean">
    </attr>
    <attr name="chipIconSize" format="dimension">
    </attr>
    <attr name="chipIconTint" format="color">
    </attr>
    <attr name="chipIconVisible" format="boolean">
    </attr>
    <attr name="chipMinHeight" format="dimension">
    </attr>
    <attr name="chipSpacing" format="dimension">
    </attr>
    <attr name="chipSpacingHorizontal" format="dimension">
    </attr>
    <attr name="chipSpacingVertical" format="dimension">
    </attr>
    <attr name="chipStandaloneStyle" format="reference">
    </attr>
    <attr name="chipStartPadding" format="dimension">
    </attr>
    <attr name="chipStrokeColor" format="color">
    </attr>
    <attr name="chipStrokeWidth" format="dimension">
    </attr>
    <attr name="chipStyle" format="reference">
    </attr>
    <attr name="closeIcon" format="reference">
    </attr>
    <attr name="closeIconEnabled" format="boolean">
    </attr>
    <attr name="closeIconEndPadding" format="dimension">
    </attr>
    <attr name="closeIconSize" format="dimension">
    </attr>
    <attr name="closeIconStartPadding" format="dimension">
    </attr>
    <attr name="closeIconTint" format="color">
    </attr>
    <attr name="closeIconVisible" format="boolean">
    </attr>
    <attr name="closeItemLayout" format="reference">
    </attr>
    <attr name="collapseContentDescription" format="string">
    </attr>
    <attr name="collapseIcon" format="reference">
    </attr>
    <attr name="collapsedTitleGravity">
        <flag name="bottom" value="80" />
        <flag name="center" value="17" />
        <flag name="center_horizontal" value="1" />
        <flag name="center_vertical" value="16" />
        <flag name="end" value="8388613" />
        <flag name="fill_vertical" value="112" />
        <flag name="left" value="3" />
        <flag name="right" value="5" />
        <flag name="start" value="8388611" />
        <flag name="top" value="48" />
    </attr>
    <attr name="collapsedTitleTextAppearance" format="reference">
    </attr>
    <attr name="color" format="color">
    </attr>
    <attr name="colorAccent" format="color">
    </attr>
    <attr name="colorBackgroundFloating" format="color">
    </attr>
    <attr name="colorButtonNormal" format="color">
    </attr>
    <attr name="colorControlActivated" format="color">
    </attr>
    <attr name="colorControlHighlight" format="color">
    </attr>
    <attr name="colorControlNormal" format="color">
    </attr>
    <attr name="colorError" format="reference|color">
    </attr>
    <attr name="colorPrimary" format="color">
    </attr>
    <attr name="colorPrimaryDark" format="color">
    </attr>
    <attr name="colorSecondary" format="color">
    </attr>
    <attr name="colorSwitchThumbNormal" format="color">
    </attr>
    <attr name="commitIcon" format="reference">
    </attr>
    <attr name="contentDescription" format="string">
    </attr>
    <attr name="contentInsetEnd" format="dimension">
    </attr>
    <attr name="contentInsetEndWithActions" format="dimension">
    </attr>
    <attr name="contentInsetLeft" format="dimension">
    </attr>
    <attr name="contentInsetRight" format="dimension">
    </attr>
    <attr name="contentInsetStart" format="dimension">
    </attr>
    <attr name="contentInsetStartWithNavigation" format="dimension">
    </attr>
    <attr name="contentPadding" format="dimension">
    </attr>
    <attr name="contentPaddingBottom" format="dimension">
    </attr>
    <attr name="contentPaddingLeft" format="dimension">
    </attr>
    <attr name="contentPaddingRight" format="dimension">
    </attr>
    <attr name="contentPaddingTop" format="dimension">
    </attr>
    <attr name="contentScrim" format="color">
    </attr>
    <attr name="controlBackground" format="reference">
    </attr>
    <attr name="coordinatorLayoutStyle" format="reference">
    </attr>
    <attr name="cornerRadius" format="dimension">
    </attr>
    <attr name="counterEnabled" format="boolean">
    </attr>
    <attr name="counterMaxLength" format="integer">
    </attr>
    <attr name="counterOverflowTextAppearance" format="reference">
    </attr>
    <attr name="counterTextAppearance" format="reference">
    </attr>
    <attr name="customNavigationLayout" format="reference">
    </attr>
    <attr name="defaultQueryHint" format="string">
    </attr>
    <attr name="dialogCornerRadius" format="dimension">
    </attr>
    <attr name="dialogPreferredPadding" format="dimension">
    </attr>
    <attr name="dialogTheme" format="reference">
    </attr>
    <attr name="displayOptions">
        <flag name="disableHome" value="32" />
        <flag name="homeAsUp" value="4" />
        <flag name="none" value="0" />
        <flag name="showCustom" value="16" />
        <flag name="showHome" value="2" />
        <flag name="showTitle" value="8" />
        <flag name="useLogo" value="1" />
    </attr>
    <attr name="divider" format="reference">
    </attr>
    <attr name="dividerHorizontal" format="reference">
    </attr>
    <attr name="dividerPadding" format="dimension">
    </attr>
    <attr name="dividerVertical" format="reference">
    </attr>
    <attr name="drawableSize" format="dimension">
    </attr>
    <attr name="drawerArrowStyle" format="reference">
    </attr>
    <attr name="dropDownListViewStyle" format="reference">
    </attr>
    <attr name="dropdownListPreferredItemHeight" format="dimension">
    </attr>
    <attr name="editTextBackground" format="reference">
    </attr>
    <attr name="editTextColor" format="reference|color">
    </attr>
    <attr name="editTextStyle" format="reference">
    </attr>
    <attr name="elevation" format="dimension">
    </attr>
    <attr name="enforceMaterialTheme" format="boolean">
    </attr>
    <attr name="enforceTextAppearance" format="boolean">
    </attr>
    <attr name="errorEnabled" format="boolean">
    </attr>
    <attr name="errorTextAppearance" format="reference">
    </attr>
    <attr name="expandActivityOverflowButtonDrawable" format="reference">
    </attr>
    <attr name="expanded" format="boolean">
    </attr>
    <attr name="expandedTitleGravity">
        <flag name="bottom" value="80" />
        <flag name="center" value="17" />
        <flag name="center_horizontal" value="1" />
        <flag name="center_vertical" value="16" />
        <flag name="end" value="8388613" />
        <flag name="fill_vertical" value="112" />
        <flag name="left" value="3" />
        <flag name="right" value="5" />
        <flag name="start" value="8388611" />
        <flag name="top" value="48" />
    </attr>
    <attr name="expandedTitleMargin" format="dimension">
    </attr>
    <attr name="expandedTitleMarginBottom" format="dimension">
    </attr>
    <attr name="expandedTitleMarginEnd" format="dimension">
    </attr>
    <attr name="expandedTitleMarginStart" format="dimension">
    </attr>
    <attr name="expandedTitleMarginTop" format="dimension">
    </attr>
    <attr name="expandedTitleTextAppearance" format="reference">
    </attr>
    <attr name="fabAlignmentMode">
        <enum name="center" value="0" />
        <enum name="end" value="1" />
    </attr>
    <attr name="fabCradleMargin" format="dimension">
    </attr>
    <attr name="fabCradleRoundedCornerRadius" format="dimension">
    </attr>
    <attr name="fabCradleVerticalOffset" format="dimension">
    </attr>
    <attr name="fabCustomSize" format="dimension">
    </attr>
    <attr name="fabSize">
        <enum name="auto" value="-1" />
        <enum name="mini" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fastScrollEnabled" format="boolean">
    </attr>
    <attr name="fastScrollHorizontalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollHorizontalTrackDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalTrackDrawable" format="reference">
    </attr>
    <attr name="firstBaselineToTopHeight" format="dimension">
    </attr>
    <attr name="floatingActionButtonStyle" format="reference|string|integer|boolean|color|float|dimension|fraction">
    </attr>
    <attr name="font" format="reference">
    </attr>
    <attr name="fontFamily" format="string">
    </attr>
    <attr name="fontProviderAuthority" format="string">
    </attr>
    <attr name="fontProviderCerts" format="reference">
    </attr>
    <attr name="fontProviderFetchStrategy">
        <enum name="async" value="1" />
        <enum name="blocking" value="0" />
    </attr>
    <attr name="fontProviderFetchTimeout" format="integer">
        <enum name="forever" value="-1" />
    </attr>
    <attr name="fontProviderPackage" format="string">
    </attr>
    <attr name="fontProviderQuery" format="string">
    </attr>
    <attr name="fontStyle">
        <enum name="italic" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fontVariationSettings" format="string">
    </attr>
    <attr name="fontWeight" format="integer">
    </attr>
    <attr name="foregroundInsidePadding" format="boolean">
    </attr>
    <attr name="gapBetweenBars" format="dimension">
    </attr>
    <attr name="goIcon" format="reference">
    </attr>
    <attr name="headerLayout" format="reference">
    </attr>
    <attr name="height" format="dimension">
    </attr>
    <attr name="helperText" format="string">
    </attr>
    <attr name="helperTextEnabled" format="boolean">
    </attr>
    <attr name="helperTextTextAppearance" format="reference">
    </attr>
    <attr name="hideMotionSpec" format="reference">
    </attr>
    <attr name="hideOnContentScroll" format="boolean">
    </attr>
    <attr name="hideOnScroll" format="boolean">
    </attr>
    <attr name="hintAnimationEnabled" format="boolean">
    </attr>
    <attr name="hintEnabled" format="boolean">
    </attr>
    <attr name="hintTextAppearance" format="reference">
    </attr>
    <attr name="homeAsUpIndicator" format="reference">
    </attr>
    <attr name="homeLayout" format="reference">
    </attr>
    <attr name="hoveredFocusedTranslationZ" format="dimension">
    </attr>
    <attr name="icon" format="reference">
    </attr>
    <attr name="iconEndPadding" format="dimension">
    </attr>
    <attr name="iconGravity">
        <flag name="start" value="1" />
        <flag name="textStart" value="2" />
    </attr>
    <attr name="iconPadding" format="dimension">
    </attr>
    <attr name="iconSize" format="dimension">
    </attr>
    <attr name="iconStartPadding" format="dimension">
    </attr>
    <attr name="iconTint" format="color">
    </attr>
    <attr name="iconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="iconifiedByDefault" format="boolean">
    </attr>
    <attr name="imageButtonStyle" format="reference">
    </attr>
    <attr name="indeterminateProgressStyle" format="reference">
    </attr>
    <attr name="initialActivityCount" format="string">
    </attr>
    <attr name="insetForeground" format="reference|color">
    </attr>
    <attr name="isLightTheme" format="boolean">
    </attr>
    <attr name="itemBackground" format="reference">
    </attr>
    <attr name="itemHorizontalPadding" format="dimension">
    </attr>
    <attr name="itemHorizontalTranslationEnabled" format="boolean">
    </attr>
    <attr name="itemIconPadding" format="dimension">
    </attr>
    <attr name="itemIconSize" format="dimension">
    </attr>
    <attr name="itemIconTint" format="color">
    </attr>
    <attr name="itemPadding" format="dimension">
    </attr>
    <attr name="itemSpacing" format="dimension">
    </attr>
    <attr name="itemTextAppearance" format="reference">
    </attr>
    <attr name="itemTextAppearanceActive" format="reference">
    </attr>
    <attr name="itemTextAppearanceInactive" format="reference">
    </attr>
    <attr name="itemTextColor" format="color">
    </attr>
    <attr name="keylines" format="reference">
    </attr>
    <attr name="labelVisibilityMode">
        <enum name="auto" value="-1" />
        <enum name="labeled" value="1" />
        <enum name="selected" value="0" />
        <enum name="unlabeled" value="2" />
    </attr>
    <attr name="lastBaselineToBottomHeight" format="dimension">
    </attr>
    <attr name="layout" format="reference">
    </attr>
    <attr name="layoutManager" format="string">
    </attr>
    <attr name="layout_anchor" format="reference">
    </attr>
    <attr name="layout_anchorGravity">
        <flag name="bottom" value="80" />
        <flag name="center" value="17" />
        <flag name="center_horizontal" value="1" />
        <flag name="center_vertical" value="16" />
        <flag name="clip_horizontal" value="8" />
        <flag name="clip_vertical" value="128" />
        <flag name="end" value="8388613" />
        <flag name="fill" value="119" />
        <flag name="fill_horizontal" value="7" />
        <flag name="fill_vertical" value="112" />
        <flag name="left" value="3" />
        <flag name="right" value="5" />
        <flag name="start" value="8388611" />
        <flag name="top" value="48" />
    </attr>
    <attr name="layout_behavior" format="string">
    </attr>
    <attr name="layout_collapseMode">
        <enum name="none" value="0" />
        <enum name="parallax" value="2" />
        <enum name="pin" value="1" />
    </attr>
    <attr name="layout_collapseParallaxMultiplier" format="float">
    </attr>
    <attr name="layout_dodgeInsetEdges">
        <flag name="all" value="119" />
        <flag name="bottom" value="80" />
        <flag name="end" value="8388613" />
        <flag name="left" value="3" />
        <flag name="none" value="0" />
        <flag name="right" value="5" />
        <flag name="start" value="8388611" />
        <flag name="top" value="48" />
    </attr>
    <attr name="layout_insetEdge">
        <enum name="bottom" value="80" />
        <enum name="end" value="8388613" />
        <enum name="left" value="3" />
        <enum name="none" value="0" />
        <enum name="right" value="5" />
        <enum name="start" value="8388611" />
        <enum name="top" value="48" />
    </attr>
    <attr name="layout_keyline" format="integer">
    </attr>
    <attr name="layout_scrollFlags">
        <flag name="enterAlways" value="4" />
        <flag name="enterAlwaysCollapsed" value="8" />
        <flag name="exitUntilCollapsed" value="2" />
        <flag name="scroll" value="1" />
        <flag name="snap" value="16" />
        <flag name="snapMargins" value="32" />
    </attr>
    <attr name="layout_scrollInterpolator" format="reference">
    </attr>
    <attr name="liftOnScroll" format="boolean">
    </attr>
    <attr name="lineHeight" format="dimension">
    </attr>
    <attr name="lineSpacing" format="dimension">
    </attr>
    <attr name="listChoiceBackgroundIndicator" format="reference">
    </attr>
    <attr name="listDividerAlertDialog" format="reference">
    </attr>
    <attr name="listItemLayout" format="reference">
    </attr>
    <attr name="listLayout" format="reference">
    </attr>
    <attr name="listMenuViewStyle" format="reference">
    </attr>
    <attr name="listPopupWindowStyle" format="reference">
    </attr>
    <attr name="listPreferredItemHeight" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightLarge" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightSmall" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingLeft" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingRight" format="dimension">
    </attr>
    <attr name="logo" format="reference">
    </attr>
    <attr name="logoDescription" format="string">
    </attr>
    <attr name="materialButtonStyle" format="reference">
    </attr>
    <attr name="materialCardViewStyle" format="reference">
    </attr>
    <attr name="maxActionInlineWidth" format="dimension">
    </attr>
    <attr name="maxButtonHeight" format="dimension">
    </attr>
    <attr name="maxImageSize" format="dimension">
    </attr>
    <attr name="measureWithLargestChild" format="boolean">
    </attr>
    <attr name="menu" format="reference">
    </attr>
    <attr name="multiChoiceItemLayout" format="reference">
    </attr>
    <attr name="navigationContentDescription" format="string">
    </attr>
    <attr name="navigationIcon" format="reference">
    </attr>
    <attr name="navigationMode">
        <enum name="listMode" value="1" />
        <enum name="normal" value="0" />
        <enum name="tabMode" value="2" />
    </attr>
    <attr name="navigationViewStyle" format="reference">
    </attr>
    <attr name="numericModifiers">
        <flag name="ALT" value="2" />
        <flag name="CTRL" value="4096" />
        <flag name="FUNCTION" value="8" />
        <flag name="META" value="65536" />
        <flag name="SHIFT" value="1" />
        <flag name="SYM" value="4" />
    </attr>
    <attr name="overlapAnchor" format="boolean">
    </attr>
    <attr name="paddingBottomNoButtons" format="dimension">
    </attr>
    <attr name="paddingEnd" format="dimension">
    </attr>
    <attr name="paddingStart" format="dimension">
    </attr>
    <attr name="paddingTopNoTitle" format="dimension">
    </attr>
    <attr name="panelBackground" format="reference">
    </attr>
    <attr name="panelMenuListTheme" format="reference">
    </attr>
    <attr name="panelMenuListWidth" format="dimension">
    </attr>
    <attr name="passwordToggleContentDescription" format="string">
    </attr>
    <attr name="passwordToggleDrawable" format="reference">
    </attr>
    <attr name="passwordToggleEnabled" format="boolean">
    </attr>
    <attr name="passwordToggleTint" format="color">
    </attr>
    <attr name="passwordToggleTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="popupMenuStyle" format="reference">
    </attr>
    <attr name="popupTheme" format="reference">
    </attr>
    <attr name="popupWindowStyle" format="reference">
    </attr>
    <attr name="preserveIconSpacing" format="boolean">
    </attr>
    <attr name="pressedTranslationZ" format="dimension">
    </attr>
    <attr name="progressBarPadding" format="dimension">
    </attr>
    <attr name="progressBarStyle" format="reference">
    </attr>
    <attr name="queryBackground" format="reference">
    </attr>
    <attr name="queryHint" format="string">
    </attr>
    <attr name="radioButtonStyle" format="reference">
    </attr>
    <attr name="ratingBarStyle" format="reference">
    </attr>
    <attr name="ratingBarStyleIndicator" format="reference">
    </attr>
    <attr name="ratingBarStyleSmall" format="reference">
    </attr>
    <attr name="reverseLayout" format="boolean">
    </attr>
    <attr name="rippleColor" format="color">
    </attr>
    <attr name="scrimAnimationDuration" format="integer">
    </attr>
    <attr name="scrimBackground" format="reference|color">
    </attr>
    <attr name="scrimVisibleHeightTrigger" format="dimension">
    </attr>
    <attr name="searchHintIcon" format="reference">
    </attr>
    <attr name="searchIcon" format="reference">
    </attr>
    <attr name="searchViewStyle" format="reference">
    </attr>
    <attr name="seekBarStyle" format="reference">
    </attr>
    <attr name="selectableItemBackground" format="reference">
    </attr>
    <attr name="selectableItemBackgroundBorderless" format="reference">
    </attr>
    <attr name="showAsAction">
        <flag name="always" value="2" />
        <flag name="collapseActionView" value="8" />
        <flag name="ifRoom" value="1" />
        <flag name="never" value="0" />
        <flag name="withText" value="4" />
    </attr>
    <attr name="showDividers">
        <flag name="beginning" value="1" />
        <flag name="end" value="4" />
        <flag name="middle" value="2" />
        <flag name="none" value="0" />
    </attr>
    <attr name="showMotionSpec" format="reference">
    </attr>
    <attr name="showText" format="boolean">
    </attr>
    <attr name="showTitle" format="boolean">
    </attr>
    <attr name="singleChoiceItemLayout" format="reference">
    </attr>
    <attr name="singleLine" format="boolean">
    </attr>
    <attr name="singleSelection" format="boolean">
    </attr>
    <attr name="snackbarButtonStyle" format="reference">
    </attr>
    <attr name="snackbarStyle" format="reference">
    </attr>
    <attr name="spanCount" format="integer">
    </attr>
    <attr name="spinBars" format="boolean">
    </attr>
    <attr name="spinnerDropDownItemStyle" format="reference">
    </attr>
    <attr name="spinnerStyle" format="reference">
    </attr>
    <attr name="splitTrack" format="boolean">
    </attr>
    <attr name="srcCompat" format="reference">
    </attr>
    <attr name="stackFromEnd" format="boolean">
    </attr>
    <attr name="state_above_anchor" format="boolean">
    </attr>
    <attr name="state_collapsed" format="boolean">
    </attr>
    <attr name="state_collapsible" format="boolean">
    </attr>
    <attr name="state_liftable" format="boolean">
    </attr>
    <attr name="state_lifted" format="boolean">
    </attr>
    <attr name="statusBarBackground" format="reference|color">
    </attr>
    <attr name="statusBarScrim" format="color">
    </attr>
    <attr name="strokeColor" format="color">
    </attr>
    <attr name="strokeWidth" format="dimension">
    </attr>
    <attr name="subMenuArrow" format="reference">
    </attr>
    <attr name="submitBackground" format="reference">
    </attr>
    <attr name="subtitle" format="string">
    </attr>
    <attr name="subtitleTextAppearance" format="reference">
    </attr>
    <attr name="subtitleTextColor" format="color">
    </attr>
    <attr name="subtitleTextStyle" format="reference">
    </attr>
    <attr name="suggestionRowLayout" format="reference">
    </attr>
    <attr name="switchMinWidth" format="dimension">
    </attr>
    <attr name="switchPadding" format="dimension">
    </attr>
    <attr name="switchStyle" format="reference">
    </attr>
    <attr name="switchTextAppearance" format="reference">
    </attr>
    <attr name="tabBackground" format="reference">
    </attr>
    <attr name="tabContentStart" format="dimension">
    </attr>
    <attr name="tabGravity">
        <enum name="center" value="1" />
        <enum name="fill" value="0" />
    </attr>
    <attr name="tabIconTint" format="color">
    </attr>
    <attr name="tabIconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tabIndicator" format="reference">
    </attr>
    <attr name="tabIndicatorAnimationDuration" format="integer">
    </attr>
    <attr name="tabIndicatorColor" format="color">
    </attr>
    <attr name="tabIndicatorFullWidth" format="boolean">
    </attr>
    <attr name="tabIndicatorGravity">
        <enum name="bottom" value="0" />
        <enum name="center" value="1" />
        <enum name="stretch" value="3" />
        <enum name="top" value="2" />
    </attr>
    <attr name="tabIndicatorHeight" format="dimension">
    </attr>
    <attr name="tabInlineLabel" format="boolean">
    </attr>
    <attr name="tabMaxWidth" format="dimension">
    </attr>
    <attr name="tabMinWidth" format="dimension">
    </attr>
    <attr name="tabMode">
        <enum name="fixed" value="1" />
        <enum name="scrollable" value="0" />
    </attr>
    <attr name="tabPadding" format="dimension">
    </attr>
    <attr name="tabPaddingBottom" format="dimension">
    </attr>
    <attr name="tabPaddingEnd" format="dimension">
    </attr>
    <attr name="tabPaddingStart" format="dimension">
    </attr>
    <attr name="tabPaddingTop" format="dimension">
    </attr>
    <attr name="tabRippleColor" format="color">
    </attr>
    <attr name="tabSelectedTextColor" format="color">
    </attr>
    <attr name="tabStyle" format="reference">
    </attr>
    <attr name="tabTextAppearance" format="reference">
    </attr>
    <attr name="tabTextColor" format="color">
    </attr>
    <attr name="tabUnboundedRipple" format="boolean">
    </attr>
    <attr name="textAllCaps" format="reference|boolean">
    </attr>
    <attr name="textAppearanceBody1" format="reference">
    </attr>
    <attr name="textAppearanceBody2" format="reference">
    </attr>
    <attr name="textAppearanceButton" format="reference">
    </attr>
    <attr name="textAppearanceCaption" format="reference">
    </attr>
    <attr name="textAppearanceHeadline1" format="reference">
    </attr>
    <attr name="textAppearanceHeadline2" format="reference">
    </attr>
    <attr name="textAppearanceHeadline3" format="reference">
    </attr>
    <attr name="textAppearanceHeadline4" format="reference">
    </attr>
    <attr name="textAppearanceHeadline5" format="reference">
    </attr>
    <attr name="textAppearanceHeadline6" format="reference">
    </attr>
    <attr name="textAppearanceLargePopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceListItem" format="reference">
    </attr>
    <attr name="textAppearanceListItemSecondary" format="reference">
    </attr>
    <attr name="textAppearanceListItemSmall" format="reference">
    </attr>
    <attr name="textAppearanceOverline" format="reference">
    </attr>
    <attr name="textAppearancePopupMenuHeader" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultSubtitle" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultTitle" format="reference">
    </attr>
    <attr name="textAppearanceSmallPopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle1" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle2" format="reference">
    </attr>
    <attr name="textColorAlertDialogListItem" format="reference|color">
    </attr>
    <attr name="textColorSearchUrl" format="reference|color">
    </attr>
    <attr name="textEndPadding" format="dimension">
    </attr>
    <attr name="textInputStyle" format="reference">
    </attr>
    <attr name="textStartPadding" format="dimension">
    </attr>
    <attr name="theme" format="reference">
    </attr>
    <attr name="thickness" format="dimension">
    </attr>
    <attr name="thumbTextPadding" format="dimension">
    </attr>
    <attr name="thumbTint" format="color">
    </attr>
    <attr name="thumbTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tickMark" format="reference">
    </attr>
    <attr name="tickMarkTint" format="color">
    </attr>
    <attr name="tickMarkTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tint" format="color">
    </attr>
    <attr name="tintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="title" format="string">
    </attr>
    <attr name="titleEnabled" format="boolean">
    </attr>
    <attr name="titleMargin" format="dimension">
    </attr>
    <attr name="titleMarginBottom" format="dimension">
    </attr>
    <attr name="titleMarginEnd" format="dimension">
    </attr>
    <attr name="titleMarginStart" format="dimension">
    </attr>
    <attr name="titleMarginTop" format="dimension">
    </attr>
    <attr name="titleMargins" format="dimension">
    </attr>
    <attr name="titleTextAppearance" format="reference">
    </attr>
    <attr name="titleTextColor" format="color">
    </attr>
    <attr name="titleTextStyle" format="reference">
    </attr>
    <attr name="toolbarId" format="reference">
    </attr>
    <attr name="toolbarNavigationButtonStyle" format="reference">
    </attr>
    <attr name="toolbarStyle" format="reference">
    </attr>
    <attr name="tooltipForegroundColor" format="reference|color">
    </attr>
    <attr name="tooltipFrameBackground" format="reference">
    </attr>
    <attr name="tooltipText" format="string">
    </attr>
    <attr name="track" format="reference">
    </attr>
    <attr name="trackTint" format="color">
    </attr>
    <attr name="trackTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="ttcIndex" format="integer">
    </attr>
    <attr name="useCompatPadding" format="boolean">
    </attr>
    <attr name="viewInflaterClass" format="string">
    </attr>
    <attr name="voiceIcon" format="reference">
    </attr>
    <attr name="windowActionBar" format="boolean">
    </attr>
    <attr name="windowActionBarOverlay" format="boolean">
    </attr>
    <attr name="windowActionModeOverlay" format="boolean">
    </attr>
    <attr name="windowFixedHeightMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedHeightMinor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowNoTitle" format="boolean">
    </attr>
</resources>
