<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AlertDialog.AppCompat" parent="@style/Base.AlertDialog.AppCompat">
    </style>
    <style name="AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat.Light">
    </style>
    <style name="Animation.AppCompat.Dialog" parent="@style/Base.Animation.AppCompat.Dialog">
    </style>
    <style name="Animation.AppCompat.DropDownUp" parent="@style/Base.Animation.AppCompat.DropDownUp">
    </style>
    <style name="Animation.AppCompat.Tooltip" parent="@style/Base.Animation.AppCompat.Tooltip">
    </style>
    <style name="Animation.Design.BottomSheetDialog" parent="@style/Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/design_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/design_bottom_sheet_slide_out</item>
    </style>
    <style name="AppTheme" parent="@style/Theme.AppCompat.Light.DarkActionBar">
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
    </style>
    <style name="AppTheme.AppBarOverlay" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
    </style>
    <style name="AppTheme.NoActionBar" parent="@style/AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="AppTheme.PopupOverlay" parent="@style/ThemeOverlay.AppCompat.Light">
    </style>
    <style name="Base.AlertDialog.AppCompat" parent="@android:style/Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat">
    </style>
    <style name="Base.Animation.AppCompat.Dialog" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.CardView" parent="@android:style/Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="cardUseCompatPadding">false</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlight</item>
        <item name="android:textColorHint">?android:attr/textColorHint</item>
        <item name="android:textColorLink">?android:attr/textColorLink</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textAllCaps">true</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium">
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">0x0</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textColorHint">?android:attr/textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">18sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small">
        <item name="android:textColor">?android:attr/textColorTertiaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textColor">?attr/actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="@style/TextAppearance.AppCompat.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?attr/colorAccent</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Menu">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Menu">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@style/TextAppearance.AppCompat.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/TextAppearance.AppCompat.Menu">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@android:style/TextAppearance.Medium">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V7.Theme.AppCompat.Dialog">
    </style>
    <style name="Base.Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="@style/Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents">
    </style>
    <style name="Base.Theme.MaterialComponents.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Dialog">
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.DialogWhenLarge" parent="@style/Theme.MaterialComponents">
    </style>
    <style name="Base.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Theme.MaterialComponents.Light">
    </style>
    <style name="Base.ThemeOverlay.AppCompat" parent="@style/Platform.ThemeOverlay.AppCompat">
    </style>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.V7.ThemeOverlay.AppCompat.Dialog">
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog">
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert">
    </style>
    <style name="Base.V14.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView.Colored</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout.Colored</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Bridge" parent="@style/Platform.MaterialComponents">
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog" parent="@style/Platform.MaterialComponents.Dialog">
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView.Colored</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout.Colored</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Bridge" parent="@style/Platform.MaterialComponents.Light">
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Theme.AppCompat.Light.DarkActionBar">
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog" parent="@style/Platform.MaterialComponents.Light.Dialog">
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="colorSecondary">?attr/colorPrimary</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" parent="@style/ThemeOverlay.AppCompat.Dialog">
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V7.Theme.AppCompat" parent="@style/Platform.AppCompat">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorError">@color/error_color_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">false</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="viewInflaterClass">android.support.v7.app.AppCompatViewInflater</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">0x20</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="@style/Platform.AppCompat.Light">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorError">@color/error_color_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">true</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="viewInflaterClass">android.support.v7.app.AppCompatViewInflater</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">0x20</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">0x20</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.AutoCompleteTextView">
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="@android:style/Widget.EditText">
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="@android:style/Widget">
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="buttonGravity">top</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="titleMargin">4dp</item>
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="android:gravity">0x10</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="background">@null</item>
        <item name="backgroundSplit">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="displayOptions">showTitle</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="dividerPadding">8dp</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:ellipsize">4</item>
        <item name="android:maxWidth">180dp</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:gravity">0x1</item>
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:minWidth">80dp</item>
        <item name="android:layout_weight">1</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="@style/RtlUnderlay.Widget.AppCompat.ActionButton">
        <item name="android:gravity">0x11</item>
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:scaleType">5</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">0x11</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="dividerPadding">6dp</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.V7.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget">
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:gravity">0x11</item>
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:minWidth">88dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
        <item name="android:background">@drawable/abc_btn_colored_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:button">?android:attr/listChoiceIndicatorMultiple</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.CompoundButton.RadioButton">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:button">?android:attr/listChoiceIndicatorSingle</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="@android:style/Widget.CompoundButton">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18dp</item>
        <item name="drawableSize">24dp</item>
        <item name="gapBetweenBars">3dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="arrowHeadLength">8dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:gravity">0x10</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="@style/Base.V7.Widget.AppCompat.EditText">
    </style>
    <style name="Base.Widget.AppCompat.ImageButton" parent="@android:style/Widget.ImageButton">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="@android:style/Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownWidth">-2</item>
        <item name="android:dropDownHorizontalOffset">0dp</item>
        <item name="android:dropDownVerticalOffset">0dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.ListView">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView">
        <item name="android:divider">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="@android:style/Widget.ListView.Menu">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:divider">?attr/dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="@android:style/Widget.PopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Holo.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Holo.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.RatingBar">
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_material</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="@android:style/Widget.RatingBar">
        <item name="android:maxHeight">36dp</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:thumb">@null</item>
        <item name="android:isIndicator">true</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="@android:style/Widget.RatingBar">
        <item name="android:maxHeight">16dp</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16dp</item>
        <item name="android:thumb">@null</item>
        <item name="android:isIndicator">true</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="@android:style/Widget">
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView">
        <item name="defaultQueryHint">@string/abc_search_hint</item>
        <item name="queryBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="submitBackground">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="@android:style/Widget">
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:focusable">true</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:progressDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:thumb">@drawable/abc_seekbar_thumb_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="@style/Platform.Widget.AppCompat.Spinner">
        <item name="android:gravity">0x800013</item>
        <item name="android:background">@drawable/abc_spinner_mtrl_am_alpha</item>
        <item name="android:clickable">true</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownWidth">-2</item>
        <item name="android:dropDownHorizontalOffset">0dp</item>
        <item name="android:dropDownVerticalOffset">0dp</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="@style/Base.V7.Widget.AppCompat.Toolbar">
    </style>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:scaleType">5</item>
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.Design.TabLayout" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="tabIconTint">@null</item>
        <item name="tabIndicator">@drawable/mtrl_tabs_default_indicator</item>
        <item name="tabIndicatorAnimationDuration">@integer/design_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?attr/colorAccent</item>
        <item name="tabIndicatorGravity">bottom</item>
        <item name="tabMaxWidth">@dimen/design_tab_max_width</item>
        <item name="tabPaddingEnd">12dp</item>
        <item name="tabPaddingStart">12dp</item>
        <item name="tabRippleColor">?attr/colorControlHighlight</item>
        <item name="tabTextAppearance">@style/TextAppearance.Design.Tab</item>
        <item name="tabUnboundedRipple">false</item>
    </style>
    <style name="Base.Widget.MaterialComponents.Chip" parent="@android:style/Widget">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:text">@null</item>
        <item name="android:checkable">false</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
        <item name="checkedIconVisible">true</item>
        <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
        <item name="chipCornerRadius">16dp</item>
        <item name="chipEndPadding">6dp</item>
        <item name="chipIcon">@null</item>
        <item name="chipIconSize">24dp</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">32dp</item>
        <item name="chipStartPadding">4dp</item>
        <item name="chipStrokeColor">#00000000</item>
        <item name="chipStrokeWidth">0dp</item>
        <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>
        <item name="closeIconEndPadding">2dp</item>
        <item name="closeIconSize">18dp</item>
        <item name="closeIconStartPadding">2dp</item>
        <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>
        <item name="closeIconVisible">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconEndPadding">0dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="rippleColor">@color/mtrl_chip_ripple_color</item>
        <item name="textEndPadding">6dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="@style/Widget.AppCompat.EditText">
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingRight">12dp</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputLayout" parent="@style/Widget.Design.TextInputLayout">
        <item name="boxBackgroundColor">@null</item>
        <item name="boxBackgroundMode">outline</item>
        <item name="boxCollapsedPaddingTop">0dp</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxCornerRadiusBottomStart">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxCornerRadiusTopEnd">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxCornerRadiusTopStart">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxStrokeColor">?attr/colorControlActivated</item>
    </style>
    <style name="CardView" parent="@style/Base.CardView">
    </style>
    <style name="CardView.Dark" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>
    <style name="CardView.Light" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
    <style name="Platform.AppCompat" parent="@android:style/Theme.Holo">
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_dark</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_dark</item>
        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_dark</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="android:actionModeCutDrawable">?attr/actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?attr/actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?attr/actionModePasteDrawable</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:actionModeSelectAllDrawable">?attr/actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
    <style name="Platform.AppCompat.Light" parent="@android:style/Theme.Holo.Light">
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_light</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_light</item>
        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_light</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="android:actionModeCutDrawable">?attr/actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?attr/actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?attr/actionModePasteDrawable</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:actionModeSelectAllDrawable">?attr/actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
    <style name="Platform.MaterialComponents" parent="@style/Theme.AppCompat">
    </style>
    <style name="Platform.MaterialComponents.Dialog" parent="@style/Theme.AppCompat.Dialog">
    </style>
    <style name="Platform.MaterialComponents.Light" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Platform.MaterialComponents.Light.Dialog" parent="@style/Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat" parent="">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="@android:style/Widget.Holo.Spinner">
    </style>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="@style/Base.DialogWindowTitle.AppCompat">
        <item name="android:textAlignment">5</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="@android:style/Widget">
        <item name="android:layout_gravity">0x800013</item>
        <item name="android:paddingEnd">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="@android:style/Widget">
        <item name="android:layout_marginEnd">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="@android:style/Widget">
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="@android:style/Widget">
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="@android:style/Widget">
        <item name="android:textAlignment">6</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="@android:style/Widget">
        <item name="android:layout_marginStart">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="@android:style/Widget">
        <item name="android:textAlignment">5</item>
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="@android:style/Widget">
        <item name="android:textAlignment">5</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="@android:style/Widget">
        <item name="android:paddingStart">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingEnd">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="@android:style/Widget">
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="@android:style/Widget">
        <item name="android:layout_toStartOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="@android:style/Widget">
        <item name="android:layout_alignParentEnd">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="@style/Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toStartOf">@android:id/icon2</item>
        <item name="android:layout_toEndOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="@android:style/Widget">
        <item name="android:layout_marginStart">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="@android:style/Widget">
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingStart">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="@style/Base.TextAppearance.AppCompat">
    </style>
    <style name="TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat.Body1">
    </style>
    <style name="TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat.Body2">
    </style>
    <style name="TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat.Button">
    </style>
    <style name="TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat.Display1">
    </style>
    <style name="TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat.Display2">
    </style>
    <style name="TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat.Display3">
    </style>
    <style name="TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat.Display4">
    </style>
    <style name="TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat.Headline">
    </style>
    <style name="TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat.Large">
    </style>
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="@style/TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="@style/TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Large">
    </style>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Small">
    </style>
    <style name="TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat.Medium">
    </style>
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat.Menu">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat.Small">
    </style>
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat.Subhead">
    </style>
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat.Title">
    </style>
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Tooltip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Colored">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="@style/Base.TextAppearance.AppCompat.Widget.DropDownItem">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="@style/Base.TextAppearance.AppCompat.Widget.Switch">
    </style>
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem">
    </style>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent">
    </style>
    <style name="TextAppearance.Compat.Notification.Info" parent="@style/TextAppearance.Compat.Notification">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media" parent="@style/TextAppearance.Compat.Notification.Info">
    </style>
    <style name="TextAppearance.Compat.Notification.Line2" parent="@style/TextAppearance.Compat.Notification.Info">
    </style>
    <style name="TextAppearance.Compat.Notification.Line2.Media" parent="@style/TextAppearance.Compat.Notification.Info.Media">
    </style>
    <style name="TextAppearance.Compat.Notification.Media" parent="@style/TextAppearance.Compat.Notification">
    </style>
    <style name="TextAppearance.Compat.Notification.Time" parent="@style/TextAppearance.Compat.Notification">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time.Media" parent="@style/TextAppearance.Compat.Notification.Time">
    </style>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title">
    </style>
    <style name="TextAppearance.Compat.Notification.Title.Media" parent="@style/TextAppearance.Compat.Notification.Title">
    </style>
    <style name="TextAppearance.Design.CollapsingToolbar.Expanded" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Counter" parent="@style/TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.Design.Counter.Overflow" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.Error" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.HelperText" parent="@style/TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.Design.Hint" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">?attr/colorControlActivated</item>
    </style>
    <style name="TextAppearance.Design.Snackbar.Message" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/design_snackbar_text_size</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Tab" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/design_tab_text_size</item>
        <item name="android:textColor">@color/mtrl_tabs_legacy_text_color_selector</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body1" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body2" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Caption" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Chip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/mtrl_chip_text_size</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline1" parent="@style/TextAppearance.AppCompat.Display4">
        <item name="android:textSize">96sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline2" parent="@style/TextAppearance.AppCompat.Display3">
        <item name="android:textSize">60sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline3" parent="@style/TextAppearance.AppCompat.Display2">
        <item name="android:textSize">48sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline4" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">34sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline5" parent="@style/TextAppearance.AppCompat.Headline">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Overline" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Tab" parent="@style/TextAppearance.Design.Tab">
        <item name="android:textColor">@color/mtrl_tabs_icon_color_selector</item>
    </style>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Theme.AppCompat" parent="@style/Base.Theme.AppCompat">
    </style>
    <style name="Theme.AppCompat.CompactMenu" parent="@style/Base.Theme.AppCompat.CompactMenu">
    </style>
    <style name="Theme.AppCompat.DayNight" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="@style/Theme.AppCompat.Light.DarkActionBar">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="@style/Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="@style/Theme.AppCompat.Light.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="@style/Theme.AppCompat.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="@style/Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="@style/Theme.AppCompat.Light.NoActionBar">
    </style>
    <style name="Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat.Dialog">
    </style>
    <style name="Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light" parent="@style/Base.Theme.AppCompat.Light">
    </style>
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light.DarkActionBar">
    </style>
    <style name="Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light.NoActionBar" parent="@style/Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar" parent="@style/Theme.AppCompat">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design" parent="@style/Theme.AppCompat">
    </style>
    <style name="Theme.Design.BottomSheetDialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Theme.Design.Light.BottomSheetDialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light.NoActionBar" parent="@style/Theme.Design.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design.NoActionBar" parent="@style/Theme.Design">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents" parent="@style/Base.Theme.MaterialComponents">
    </style>
    <style name="Theme.MaterialComponents.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Bridge" parent="@style/Base.Theme.MaterialComponents.Bridge">
    </style>
    <style name="Theme.MaterialComponents.CompactMenu" parent="@style/Base.Theme.MaterialComponents.CompactMenu">
    </style>
    <style name="Theme.MaterialComponents.Dialog" parent="@style/Base.Theme.MaterialComponents.Dialog">
    </style>
    <style name="Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog.Alert">
    </style>
    <style name="Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog.MinWidth">
    </style>
    <style name="Theme.MaterialComponents.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.DialogWhenLarge">
    </style>
    <style name="Theme.MaterialComponents.Light" parent="@style/Base.Theme.MaterialComponents.Light">
    </style>
    <style name="Theme.MaterialComponents.Light.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Light.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar">
    </style>
    <style name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Alert">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.Light.DialogWhenLarge">
    </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar" parent="@style/Theme.MaterialComponents.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Light.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar" parent="@style/Theme.MaterialComponents">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="ThemeOverlay.AppCompat" parent="@style/Base.ThemeOverlay.AppCompat">
    </style>
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.ActionBar">
    </style>
    <style name="ThemeOverlay.AppCompat.Dark" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
    </style>
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark.ActionBar">
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat.Dialog">
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog.Alert">
    </style>
    <style name="ThemeOverlay.AppCompat.Light" parent="@style/Base.ThemeOverlay.AppCompat.Light">
    </style>
    <style name="ThemeOverlay.MaterialComponents" parent="@style/ThemeOverlay.AppCompat">
    </style>
    <style name="ThemeOverlay.MaterialComponents.ActionBar" parent="@style/ThemeOverlay.AppCompat.ActionBar">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dark" parent="@style/ThemeOverlay.AppCompat.Dark">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dark.ActionBar" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Light" parent="@style/ThemeOverlay.AppCompat.Light">
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText" parent="">
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="ToolbarColoredBackArrow" parent="@style/AppTheme">
        <item name="android:textColorSecondary">@color/white</item>
        <item name="android:actionMenuTextColor">@color/white</item>
    </style>
    <style name="Widget.AppCompat.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.ActionButton" parent="@style/Base.Widget.AppCompat.ActionButton">
    </style>
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton.CloseMode">
    </style>
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton.Overflow">
    </style>
    <style name="Widget.AppCompat.ActionMode" parent="@style/Base.Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.ActivityChooserView" parent="@style/Base.Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Button" parent="@style/Base.Widget.AppCompat.Button">
    </style>
    <style name="Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button.Borderless">
    </style>
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless.Colored">
    </style>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog">
    </style>
    <style name="Widget.AppCompat.Button.Colored" parent="@style/Base.Widget.AppCompat.Button.Colored">
    </style>
    <style name="Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button.Small">
    </style>
    <style name="Widget.AppCompat.ButtonBar" parent="@style/Base.Widget.AppCompat.ButtonBar">
    </style>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar.AlertDialog">
    </style>
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="@style/Base.Widget.AppCompat.CompoundButton.CheckBox">
    </style>
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="@style/Base.Widget.AppCompat.CompoundButton.RadioButton">
    </style>
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="@style/Base.Widget.AppCompat.CompoundButton.Switch">
    </style>
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text">
    </style>
    <style name="Widget.AppCompat.EditText" parent="@style/Base.Widget.AppCompat.EditText">
    </style>
    <style name="Widget.AppCompat.ImageButton" parent="@style/Base.Widget.AppCompat.ImageButton">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton" parent="@style/Widget.AppCompat.ActionButton">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="@style/Widget.AppCompat.ActionButton.CloseMode">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="@style/Widget.AppCompat.ActionButton.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="@style/Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="@style/Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="@style/Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="@style/Widget.AppCompat.DropDownItem.Spinner">
    </style>
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="@style/Widget.AppCompat.ListView.DropDown">
    </style>
    <style name="Widget.AppCompat.Light.PopupMenu" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
    </style>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.SearchView" parent="@style/Widget.AppCompat.SearchView">
    </style>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown.ActionBar">
    </style>
    <style name="Widget.AppCompat.ListMenuView" parent="@style/Base.Widget.AppCompat.ListMenuView">
    </style>
    <style name="Widget.AppCompat.ListPopupWindow" parent="@style/Base.Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.ListView" parent="@style/Base.Widget.AppCompat.ListView">
    </style>
    <style name="Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView.DropDown">
    </style>
    <style name="Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView.Menu">
    </style>
    <style name="Widget.AppCompat.PopupMenu" parent="@style/Base.Widget.AppCompat.PopupMenu">
    </style>
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.PopupWindow" parent="@style/Base.Widget.AppCompat.PopupWindow">
    </style>
    <style name="Widget.AppCompat.ProgressBar" parent="@style/Base.Widget.AppCompat.ProgressBar">
    </style>
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="@style/Base.Widget.AppCompat.ProgressBar.Horizontal">
    </style>
    <style name="Widget.AppCompat.RatingBar" parent="@style/Base.Widget.AppCompat.RatingBar">
    </style>
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="@style/Base.Widget.AppCompat.RatingBar.Indicator">
    </style>
    <style name="Widget.AppCompat.RatingBar.Small" parent="@style/Base.Widget.AppCompat.RatingBar.Small">
    </style>
    <style name="Widget.AppCompat.SearchView" parent="@style/Base.Widget.AppCompat.SearchView">
    </style>
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView.ActionBar">
    </style>
    <style name="Widget.AppCompat.SeekBar" parent="@style/Base.Widget.AppCompat.SeekBar">
    </style>
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar.Discrete">
    </style>
    <style name="Widget.AppCompat.Spinner" parent="@style/Base.Widget.AppCompat.Spinner">
    </style>
    <style name="Widget.AppCompat.Spinner.DropDown" parent="@style/Widget.AppCompat.Spinner">
    </style>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown">
    </style>
    <style name="Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner.Underlined">
    </style>
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="@style/Base.Widget.AppCompat.TextView.SpinnerItem">
    </style>
    <style name="Widget.AppCompat.Toolbar" parent="@style/Base.Widget.AppCompat.Toolbar">
    </style>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/Base.Widget.AppCompat.Toolbar.Button.Navigation">
    </style>
    <style name="Widget.Compat.NotificationActionContainer" parent="">
    </style>
    <style name="Widget.Compat.NotificationActionText" parent="">
    </style>
    <style name="Widget.Design.AppBarLayout" parent="@android:style/Widget">
        <item name="android:background">?attr/colorPrimary</item>
    </style>
    <style name="Widget.Design.BottomNavigationView" parent="">
        <item name="elevation">@dimen/design_bottom_navigation_elevation</item>
        <item name="itemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="itemHorizontalTranslationEnabled">true</item>
        <item name="itemIconSize">@dimen/design_bottom_navigation_icon_size</item>
        <item name="labelVisibilityMode">auto</item>
    </style>
    <style name="Widget.Design.BottomSheet.Modal" parent="@android:style/Widget">
        <item name="android:background">?android:attr/colorBackground</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_peekHeight">auto</item>
        <item name="behavior_skipCollapsed">false</item>
    </style>
    <style name="Widget.Design.CollapsingToolbar" parent="@android:style/Widget">
        <item name="expandedTitleMargin">32dp</item>
        <item name="statusBarScrim">?attr/colorPrimaryDark</item>
    </style>
    <style name="Widget.Design.FloatingActionButton" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_fab_background</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="backgroundTint">?attr/colorAccent</item>
        <item name="borderWidth">@dimen/design_fab_border_width</item>
        <item name="elevation">@dimen/design_fab_elevation</item>
        <item name="fabSize">auto</item>
        <item name="hideMotionSpec">@animator/design_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/design_fab_translation_z_hovered_focused</item>
        <item name="maxImageSize">@dimen/design_fab_image_size</item>
        <item name="pressedTranslationZ">@dimen/design_fab_translation_z_pressed</item>
        <item name="rippleColor">?attr/colorControlHighlight</item>
        <item name="showMotionSpec">@animator/design_fab_show_motion_spec</item>
    </style>
    <style name="Widget.Design.NavigationView" parent="">
        <item name="android:background">?android:attr/windowBackground</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:maxWidth">@dimen/design_navigation_max_width</item>
        <item name="elevation">@dimen/design_navigation_elevation</item>
        <item name="itemHorizontalPadding">@dimen/design_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/design_navigation_item_icon_padding</item>
    </style>
    <style name="Widget.Design.ScrimInsetsFrameLayout" parent="">
        <item name="insetForeground">#0000</item>
    </style>
    <style name="Widget.Design.Snackbar" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_snackbar_background</item>
        <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:maxWidth">@dimen/design_snackbar_max_width</item>
        <item name="android:minWidth">@dimen/design_snackbar_min_width</item>
        <item name="elevation">@dimen/design_snackbar_elevation</item>
        <item name="maxActionInlineWidth">@dimen/design_snackbar_action_inline_max_width</item>
    </style>
    <style name="Widget.Design.TabLayout" parent="@style/Base.Widget.Design.TabLayout">
        <item name="tabGravity">fill</item>
        <item name="tabIndicatorFullWidth">true</item>
        <item name="tabMode">fixed</item>
    </style>
    <style name="Widget.Design.TextInputLayout" parent="@android:style/Widget">
        <item name="boxBackgroundMode">none</item>
        <item name="counterOverflowTextAppearance">@style/TextAppearance.Design.Counter.Overflow</item>
        <item name="counterTextAppearance">@style/TextAppearance.Design.Counter</item>
        <item name="errorTextAppearance">@style/TextAppearance.Design.Error</item>
        <item name="helperTextTextAppearance">@style/TextAppearance.Design.HelperText</item>
        <item name="hintTextAppearance">@style/TextAppearance.Design.Hint</item>
        <item name="passwordToggleContentDescription">@string/password_toggle_content_description</item>
        <item name="passwordToggleDrawable">@drawable/design_password_eye</item>
        <item name="passwordToggleTint">@color/design_tint_password_toggle</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="backgroundTint">@android:color/white</item>
        <item name="fabCradleMargin">@dimen/mtrl_bottomappbar_fab_cradle_margin</item>
        <item name="fabCradleRoundedCornerRadius">@dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius</item>
        <item name="fabCradleVerticalOffset">@dimen/mtrl_bottomappbar_fab_cradle_vertical_offset</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar.Colored" parent="@style/Widget.MaterialComponents.BottomAppBar">
        <item name="backgroundTint">?attr/colorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView" parent="@style/Widget.Design.BottomNavigationView">
        <item name="android:background">@android:color/white</item>
        <item name="enforceTextAppearance">true</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="itemIconTint">@color/mtrl_bottom_nav_item_tint</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_bottom_nav_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.Colored" parent="@style/Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="itemIconTint">@color/mtrl_bottom_nav_colored_item_tint</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_bottom_nav_colored_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomSheet.Modal" parent="@style/Widget.Design.BottomSheet.Modal">
    </style>
    <style name="Widget.MaterialComponents.Button" parent="@style/Widget.AppCompat.Button">
        <item name="android:textAppearance">?attr/textAppearanceButton</item>
        <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>
        <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
        <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
        <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
        <item name="cornerRadius">@dimen/mtrl_btn_corner_radius</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.Icon" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="strokeColor">@color/mtrl_btn_stroke_color_selector</item>
        <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton.Icon" parent="@style/Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textColor">@color/mtrl_text_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_text_btn_padding_right</item>
        <item name="backgroundTint">@color/mtrl_btn_transparent_bg_color</item>
        <item name="iconPadding">@dimen/mtrl_btn_text_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_text_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_text_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton.Dialog">
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton">
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton" parent="@style/Widget.MaterialComponents.Button">
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.CardView" parent="@style/CardView">
        <item name="cardBackgroundColor">?attr/colorBackgroundFloating</item>
        <item name="cardElevation">@dimen/mtrl_card_elevation</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Action" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Choice" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Entry" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Filter" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.ChipGroup" parent="@android:style/Widget">
        <item name="chipSpacing">4dp</item>
        <item name="singleLine">false</item>
        <item name="singleSelection">false</item>
    </style>
    <style name="Widget.MaterialComponents.FloatingActionButton" parent="@style/Widget.Design.FloatingActionButton">
        <item name="elevation">@dimen/mtrl_fab_elevation</item>
        <item name="hideMotionSpec">@animator/mtrl_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/mtrl_fab_translation_z_hovered_focused</item>
        <item name="pressedTranslationZ">@dimen/mtrl_fab_translation_z_pressed</item>
        <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
        <item name="showMotionSpec">@animator/mtrl_fab_show_motion_spec</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationView" parent="@style/Widget.Design.NavigationView">
        <item name="elevation">@dimen/mtrl_navigation_elevation</item>
        <item name="itemHorizontalPadding">@dimen/mtrl_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/mtrl_navigation_item_icon_padding</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar" parent="@style/Widget.Design.Snackbar">
        <item name="android:background">@drawable/mtrl_snackbar_background</item>
        <item name="android:layout_margin">@dimen/mtrl_snackbar_margin</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar.FullWidth" parent="@style/Widget.Design.Snackbar">
    </style>
    <style name="Widget.MaterialComponents.TabLayout" parent="@style/Widget.Design.TabLayout">
        <item name="android:background">@android:color/white</item>
        <item name="enforceTextAppearance">true</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabIndicatorAnimationDuration">@integer/mtrl_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?attr/colorAccent</item>
        <item name="tabRippleColor">@color/mtrl_tabs_ripple_color</item>
        <item name="tabTextAppearance">?attr/textAppearanceButton</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabUnboundedRipple">true</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout.Colored" parent="@style/Widget.MaterialComponents.TabLayout">
        <item name="android:background">?attr/colorAccent</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector_colored</item>
        <item name="tabIndicatorColor">@android:color/white</item>
        <item name="tabRippleColor">@color/mtrl_tabs_colored_ripple_color</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector_colored</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText">
        <item name="android:paddingTop">20dp</item>
        <item name="android:paddingBottom">16dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.FilledBox">
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText">
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox</item>
        <item name="boxBackgroundColor">@color/mtrl_textinput_filled_box_default_background_color</item>
        <item name="boxBackgroundMode">filled</item>
        <item name="boxCollapsedPaddingTop">12dp</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/mtrl_textinput_box_corner_radius_small</item>
        <item name="boxCornerRadiusBottomStart">@dimen/mtrl_textinput_box_corner_radius_small</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
        <item name="boxCollapsedPaddingTop">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox</item>
        <item name="boxCollapsedPaddingTop">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.Toolbar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="subtitleTextColor">?android:attr/textColorSecondary</item>
        <item name="titleTextAppearance">?attr/textAppearanceHeadline6</item>
        <item name="titleTextColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Widget.Support.CoordinatorLayout" parent="@android:style/Widget">
        <item name="statusBarBackground">#000000</item>
    </style>
    <style name="courseDuration" parent="">
        <item name="android:textSize">@dimen/_16sp</item>
        <item name="android:textColor">#eeeeee</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseEmployee" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#000000</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseMemo" parent="">
        <item name="android:textSize">@dimen/_14sp</item>
        <item name="android:textColor">#000000</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseMoney" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textColor">#274ac8</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseRemainTime" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#bf6c26</item>
        <item name="android:padding">@dimen/_1dp</item>
    </style>
    <style name="courseTitle" parent="">
        <item name="android:textSize">@dimen/_22sp</item>
        <item name="android:textStyle">0x1</item>
        <item name="android:textColor">#bf6c26</item>
        <item name="android:layout_marginRight">@dimen/_5dp</item>
    </style>
    <style name="login_edit" parent="">
        <item name="android:textSize">@dimen/_16sp</item>
        <item name="android:textColor">@color/normal_text_color2</item>
        <item name="android:textColorHint">@color/normal_text_color</item>
        <item name="android:background">@drawable/signin_line</item>
        <item name="android:layout_width">-1</item>
        <item name="android:layout_height">-1</item>
        <item name="android:layout_marginStart">@dimen/_20dp</item>
        <item name="android:layout_marginEnd">@dimen/_20dp</item>
        <item name="singleLine">true</item>
    </style>
    <style name="popupTheme" parent="@style/Theme.AppCompat.DayNight.Dialog">
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="settingEdit" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#dddddd</item>
    </style>
    <style name="settingLabel" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#dddddd</item>
        <item name="android:gravity">0x11</item>
    </style>
    <style name="settingSpinner" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">#dddddd</item>
        <item name="background">@drawable/img_alpha25</item>
    </style>
    <style name="social_button" parent="">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">-1</item>
        <item name="android:layout_margin">@dimen/_3dp</item>
        <item name="android:layout_weight">1</item>
    </style>
    <style name="toolbarWhiteText" parent="">
        <item name="android:textSize">@dimen/_18sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">0x11</item>
        <item name="android:layout_width">-2</item>
        <item name="android:layout_height">-2</item>
        <item name="android:textAllCaps">false</item>
    </style>
</resources>
