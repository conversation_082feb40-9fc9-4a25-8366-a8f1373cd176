<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance.Material">
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1" parent="@android:style/TextAppearance.Material.Body1">
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2" parent="@android:style/TextAppearance.Material.Body2">
    </style>
    <style name="Base.TextAppearance.AppCompat.Button" parent="@android:style/TextAppearance.Material.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption" parent="@android:style/TextAppearance.Material.Caption">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1" parent="@android:style/TextAppearance.Material.Display1">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2" parent="@android:style/TextAppearance.Material.Display2">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3" parent="@android:style/TextAppearance.Material.Display3">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4" parent="@android:style/TextAppearance.Material.Display4">
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline" parent="@android:style/TextAppearance.Material.Headline">
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="@android:style/TextAppearance.Material.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Large" parent="@android:style/TextAppearance.Material.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@android:style/TextAppearance.Material.Large.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium" parent="@android:style/TextAppearance.Material.Medium">
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@android:style/TextAppearance.Material.Medium.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu" parent="@android:style/TextAppearance.Material.Menu">
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@android:style/TextAppearance.Material.SearchResult.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@android:style/TextAppearance.Material.SearchResult.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Small" parent="@android:style/TextAppearance.Material.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@android:style/TextAppearance.Material.Small.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="@android:style/TextAppearance.Material.Subhead">
    </style>
    <style name="Base.TextAppearance.AppCompat.Title" parent="@android:style/TextAppearance.Material.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="@android:style/TextAppearance.Material.Widget.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:fontFamily">@string/abc_font_family_title_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@android:style/TextAppearance.Material.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@android:style/TextAppearance.Material.Widget.TextView.SpinnerItem">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V21.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V21.Theme.AppCompat.Dialog">
    </style>
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V21.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V21.Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.V21.ThemeOverlay.AppCompat.Dialog">
    </style>
    <style name="Base.V14.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView.Colored</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout.Colored</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V21.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat">
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="actionOverflowButtonStyle">?android:attr/actionOverflowButtonStyle</item>
        <item name="borderlessButtonStyle">?android:attr/borderlessButtonStyle</item>
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Dialog" parent="@style/Base.V7.Theme.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light">
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="actionOverflowButtonStyle">?android:attr/actionOverflowButtonStyle</item>
        <item name="borderlessButtonStyle">?android:attr/borderlessButtonStyle</item>
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light.Dialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.V7.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="@android:style/Widget.Material.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="@android:style/Widget.Material.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="@android:style/Widget.Material.ActionButton">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@android:style/Widget.Material.ActionButton.CloseMode">
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@android:style/Widget.Material.ActionButton.Overflow">
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.Material.AutoCompleteTextView">
        <item name="android:background">?attr/editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget.Material.Button">
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="@android:style/Widget.Material.Button.Borderless">
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@android:style/Widget.Material.Button.Borderless.Colored">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small" parent="@android:style/Widget.Material.Button.Small">
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget.Material.ButtonBar">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.Material.CompoundButton.CheckBox">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.Material.CompoundButton.RadioButton">
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="@android:style/Widget.Material.DropDownItem.Spinner">
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="@android:style/Widget.Material.EditText">
        <item name="android:background">?attr/editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.ImageButton" parent="@android:style/Widget.Material.ImageButton">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@android:style/Widget.Material.Light.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@android:style/Widget.Material.Light.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@android:style/Widget.Material.Light.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@android:style/Widget.Material.Light.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="@android:style/Widget.Material.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.Material.ListView">
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="@android:style/Widget.Material.ListView.DropDown">
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@android:style/Widget.Material.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Material.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Material.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.Material.RatingBar">
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="@android:style/Widget.Material.SeekBar">
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="@android:style/Widget.Material.Spinner">
    </style>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.Material.TextView.SpinnerItem">
    </style>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget.Material.Toolbar.Button.Navigation">
    </style>
    <style name="Base.Widget.MaterialComponents.Chip" parent="@android:style/Widget">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:text">@null</item>
        <item name="android:checkable">false</item>
        <item name="android:stateListAnimator">@animator/mtrl_chip_state_list_anim</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
        <item name="checkedIconVisible">true</item>
        <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
        <item name="chipCornerRadius">16dp</item>
        <item name="chipEndPadding">6dp</item>
        <item name="chipIcon">@null</item>
        <item name="chipIconSize">24dp</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">32dp</item>
        <item name="chipStartPadding">4dp</item>
        <item name="chipStrokeColor">#00000000</item>
        <item name="chipStrokeWidth">0dp</item>
        <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>
        <item name="closeIconEndPadding">2dp</item>
        <item name="closeIconSize">18dp</item>
        <item name="closeIconStartPadding">2dp</item>
        <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>
        <item name="closeIconVisible">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconEndPadding">0dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="rippleColor">@color/mtrl_chip_ripple_color</item>
        <item name="textEndPadding">6dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Platform.AppCompat" parent="@style/Platform.V21.AppCompat">
    </style>
    <style name="Platform.AppCompat.Light" parent="@style/Platform.V21.AppCompat.Light">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat" parent="">
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat">
    </style>
    <style name="Platform.V21.AppCompat" parent="@android:style/Theme.Material.NoActionBar">
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>
    </style>
    <style name="Platform.V21.AppCompat.Light" parent="@android:style/Theme.Material.Light.NoActionBar">
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>
    </style>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.Material.Notification">
    </style>
    <style name="TextAppearance.Compat.Notification.Info" parent="@android:style/TextAppearance.Material.Notification.Info">
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media" parent="@style/TextAppearance.Compat.Notification.Info">
        <item name="android:textColor">@color/secondary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Media" parent="@style/TextAppearance.Compat.Notification">
        <item name="android:textColor">@color/secondary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time" parent="@android:style/TextAppearance.Material.Notification.Time">
    </style>
    <style name="TextAppearance.Compat.Notification.Time.Media" parent="@style/TextAppearance.Compat.Notification.Time">
        <item name="android:textColor">@color/secondary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.Material.Notification.Title">
    </style>
    <style name="TextAppearance.Compat.Notification.Title.Media" parent="@style/TextAppearance.Compat.Notification.Title">
        <item name="android:textColor">@color/primary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body1" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0312</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body2" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0179</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0893</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Caption" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0333</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline1" parent="@style/TextAppearance.AppCompat.Display4">
        <item name="android:textSize">96sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:letterSpacing">-0.0156</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline2" parent="@style/TextAppearance.AppCompat.Display3">
        <item name="android:textSize">60sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:letterSpacing">-0.0083</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline3" parent="@style/TextAppearance.AppCompat.Display2">
        <item name="android:textSize">48sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline4" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">34sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0074</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline5" parent="@style/TextAppearance.AppCompat.Headline">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0125</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Overline" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.1667</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0094</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">0x0</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0071</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Widget.Compat.NotificationActionContainer" parent="">
        <item name="android:background">@drawable/notification_action_background</item>
    </style>
    <style name="Widget.Compat.NotificationActionText" parent="">
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:textSize">@dimen/notification_action_text_size</item>
        <item name="android:textColor">@color/secondary_text_default_material_light</item>
    </style>
    <style name="Widget.Design.AppBarLayout" parent="@android:style/Widget">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="android:stateListAnimator">@animator/design_appbar_state_list_animator</item>
        <item name="android:touchscreenBlocksFocus">true</item>
    </style>
    <style name="Widget.Design.BottomSheet.Modal" parent="@android:style/Widget">
        <item name="android:background">?android:attr/colorBackground</item>
        <item name="android:elevation">@dimen/design_bottom_sheet_modal_elevation</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_peekHeight">auto</item>
        <item name="behavior_skipCollapsed">false</item>
    </style>
    <style name="Widget.MaterialComponents.Button" parent="@style/Widget.AppCompat.Button">
        <item name="android:textAppearance">?attr/textAppearanceButton</item>
        <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>
        <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
        <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
        <item name="android:stateListAnimator">@animator/mtrl_btn_state_list_anim</item>
        <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
        <item name="cornerRadius">@dimen/mtrl_btn_corner_radius</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:stateListAnimator">@animator/mtrl_btn_unelevated_state_list_anim</item>
    </style>
</resources>
