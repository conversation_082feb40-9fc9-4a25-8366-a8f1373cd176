<?xml version="1.0" encoding="utf-8"?>
<android.support.v4.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <include
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/app_bar_main"/>
    <LinearLayout
        android:layout_gravity="start"
        android:orientation="vertical"
        android:id="@+id/menuLayout"
        android:background="@color/back_color"
        android:visibility="visible"
        android:layout_width="@dimen/_280dp"
        android:layout_height="match_parent">
        <include layout="@layout/nav_header_main"/>
        <ListView
            android:id="@+id/drawerLV"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_10dp"
            android:divider="@color/back_color"
            android:dividerHeight="@dimen/_1dp"/>
    </LinearLayout>
</android.support.v4.widget.DrawerLayout>
