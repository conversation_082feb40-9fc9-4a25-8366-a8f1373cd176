<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/list_item"
    android:background="?attr/selectableItemBackground"
    android:paddingLeft="16dp"
    android:paddingRight="16dp"
    android:layout_width="match_parent"
    android:layout_height="?attr/dropdownListPreferredItemHeight"
    android:minWidth="196dp">
    <LinearLayout
        android:duplicateParentState="true"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">
        <ImageView
            android:layout_gravity="center_vertical"
            android:id="@+id/icon"
            android:duplicateParentState="true"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginRight="8dp"/>
        <TextView
            android:textAppearance="?attr/textAppearanceLargePopupMenu"
            android:ellipsize="marquee"
            android:layout_gravity="center_vertical"
            android:id="@+id/title"
            android:fadingEdge="horizontal"
            android:duplicateParentState="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"/>
    </LinearLayout>
</LinearLayout>
