<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/img_back2"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/_20sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:text="콜폭 메모 갯수"/>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="ㅋㅍ/콜폭 지정된 메모로 차단 합니다.\10[설정갯수] 이상 지정된 메모가 있는경우 자동차단 됩니다.\10"/>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/color_sync1"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="주의: ㅋㅍ/콜폭 키워드 허위메모 등록시 즉시 정지"/>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="처리되니 키워드에 대한 허위기재는\10삼가하여 주시기 바랍니다."/>
        <LinearLayout
            android:background="@drawable/default_editbox"
            android:padding="@dimen/_5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp">
            <EditText
                android:textSize="@dimen/_20sp"
                android:textColor="@color/color_sync0"
                android:id="@+id/txtCount"
                android:background="@color/white"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="phone"
                android:textAlignment="center"/>
        </LinearLayout>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/color_sync1"
            android:id="@+id/txtError"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_10dp"
            android:layout_marginStart="@dimen/_20dp"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnCancel"
                android:background="@color/color_sync0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_cancel"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnOk"
                android:background="@color/color_sync1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_ok"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
        </LinearLayout>
    </LinearLayout>
    <Button
        android:layout_gravity="end|top"
        android:id="@+id/btnClose"
        android:background="@drawable/pop_close"
        android:layout_width="@dimen/_30dp"
        android:layout_height="@dimen/_30dp"
        android:layout_margin="@dimen/_5dp"/>
</FrameLayout>
