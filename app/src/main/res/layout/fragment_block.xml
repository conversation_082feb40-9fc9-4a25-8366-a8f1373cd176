<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/blockFragment"
    android:background="@drawable/search_back"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <android.support.design.widget.TabLayout
            android:id="@+id/tabBlockCtrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabIndicatorColor="@color/color_sync0"
            app:tabIndicatorHeight="7dp"
            app:tabSelectedTextColor="@color/color_sync0"
            app:tabTextColor="@color/normal_text_color2">
            <android.support.design.widget.TabItem
                android:id="@+id/tabBlockSetting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="차단설정"/>
            <android.support.design.widget.TabItem
                android:id="@+id/tabBlockNumbers"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="차단번호"/>
            <android.support.design.widget.TabItem
                android:id="@+id/tabBlockHistory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="차단기록"/>
        </android.support.design.widget.TabLayout>
        <FrameLayout
            android:id="@+id/block_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:divider="@color/line_color1"
            android:dividerHeight="@dimen/_1dp"/>
    </LinearLayout>
</FrameLayout>
