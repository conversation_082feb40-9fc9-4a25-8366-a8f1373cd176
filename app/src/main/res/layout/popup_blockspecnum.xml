<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/img_back2"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/_20sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:text="전화번호 입력"/>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="입력하신 전화번호의\10모든 수신전화를 차단합니다.\1011자리의 번호를 입력하여 주세요"/>
        <LinearLayout
            android:background="@drawable/default_editbox"
            android:padding="@dimen/_5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp">
            <EditText
                android:textSize="@dimen/_20sp"
                android:textColor="@color/color_sync0"
                android:id="@+id/txtPhoneNumber"
                android:background="@color/white"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="phone"
                android:textAlignment="center"/>
        </LinearLayout>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/color_sync1"
            android:id="@+id/txtError"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_10dp"
            android:layout_marginStart="@dimen/_20dp"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnCancel"
                android:background="@color/color_sync0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_cancel"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnOk"
                android:background="@color/color_sync1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_ok"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
        </LinearLayout>
    </LinearLayout>
    <Button
        android:layout_gravity="end|top"
        android:id="@+id/btnClose"
        android:background="@drawable/pop_close"
        android:layout_width="@dimen/_30dp"
        android:layout_height="@dimen/_30dp"
        android:layout_margin="@dimen/_5dp"/>
</FrameLayout>
