<?xml version="1.0" encoding="utf-8"?>
<view xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_gravity="center"
    android:id="@+id/activity_chooser_view_content"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    class="android.support.v7.widget.ActivityChooserView$InnerLayout"
    style="?attr/activityChooserViewStyle">
    <FrameLayout
        android:layout_gravity="center"
        android:id="@+id/expand_activities_button"
        android:background="?attr/actionBarItemBackground"
        android:paddingLeft="12dp"
        android:paddingTop="2dp"
        android:paddingRight="12dp"
        android:paddingBottom="2dp"
        android:focusable="true"
        android:addStatesFromChildren="true"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">
        <ImageView
            android:layout_gravity="center"
            android:id="@+id/image"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:scaleType="fitCenter"
            android:adjustViewBounds="true"/>
    </FrameLayout>
    <FrameLayout
        android:layout_gravity="center"
        android:id="@+id/default_activity_button"
        android:background="?attr/actionBarItemBackground"
        android:paddingLeft="12dp"
        android:paddingTop="2dp"
        android:paddingRight="12dp"
        android:paddingBottom="2dp"
        android:focusable="true"
        android:addStatesFromChildren="true"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">
        <ImageView
            android:layout_gravity="center"
            android:id="@+id/image"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:scaleType="fitCenter"
            android:adjustViewBounds="true"/>
    </FrameLayout>
</view>
