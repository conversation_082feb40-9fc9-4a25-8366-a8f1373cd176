<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/reportFragment"
    android:background="@drawable/search_back"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ListView
        android:id="@+id/lstReport"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/viewDetail"
        android:padding="@dimen/_10dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/_22sp"
            android:textStyle="bold"
            android:textColor="@color/normal_text_color"
            android:id="@+id/txtNoticeTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_12dp"
            android:text="안녕하세요"/>
        <TextView
            android:textSize="@dimen/_12sp"
            android:textColor="@color/normal_text_color"
            android:id="@+id/txtNoticeDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/_12dp"
            android:text="2020-09-28 오후 03:08:24"/>
        <View
            android:id="@+id/divider2"
            android:background="?android:attr/listDivider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_20dp"/>
        <TextView
            android:textSize="@dimen/_18sp"
            android:textColor="@color/normal_text_color"
            android:id="@+id/txtNoticeContent"
            android:scrollbars="vertical"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="@dimen/_10dp"
            android:text="공지테스트입니다. "
            android:layout_weight="100"/>
        <Button
            android:textColor="@color/white"
            android:id="@+id/btnGoList"
            android:background="@drawable/round_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:text="목록보기"/>
        <View
            android:id="@+id/divider"
            android:background="?android:attr/listDivider"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
    </LinearLayout>
</FrameLayout>
