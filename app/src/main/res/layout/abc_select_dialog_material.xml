<?xml version="1.0" encoding="utf-8"?>
<view xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/select_dialog_listview"
    android:scrollbars="vertical"
    android:fadingEdge="none"
    android:clipToPadding="false"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:cacheColorHint="@null"
    android:divider="?attr/listDividerAlertDialog"
    android:overScrollMode="ifContentScrolls"
    android:textAlignment="viewStart"
    app:paddingBottomNoButtons="@dimen/abc_dialog_list_padding_bottom_no_buttons"
    app:paddingTopNoTitle="@dimen/abc_dialog_list_padding_top_no_title"
    class="android.support.v7.app.AlertController$RecycleListView"
    style="@style/Widget.AppCompat.ListView"/>
