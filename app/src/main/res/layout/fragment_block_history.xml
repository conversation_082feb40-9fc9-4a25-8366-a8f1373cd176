<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:background="@drawable/shape_back_lightblue_stroke"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_50dp"
            android:paddingStart="@dimen/_20dp"
            android:paddingEnd="@dimen/_20dp">
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="차단기록"
                    android:paddingStart="0dp"
                    android:paddingEnd="@dimen/_10dp"/>
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:id="@+id/txtTotalCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0 건"/>
            </LinearLayout>
            <LinearLayout
                android:gravity="center"
                android:orientation="horizontal"
                android:id="@+id/layout_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_10dp">
                <ImageView
                    android:layout_width="@dimen/_25dp"
                    android:layout_height="@dimen/_25dp"
                    android:src="@drawable/icon_close"/>
                <TextView
                    android:textColor="@color/normal_text_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" 차단기록삭제"/>
            </LinearLayout>
        </LinearLayout>
        <FrameLayout
            android:orientation="horizontal"
            android:id="@+id/layoutWatermark"
            android:background="@drawable/search_back"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <ListView
                android:id="@+id/lstBlockHistory"
                android:background="@color/white_alpha_color"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:divider="@color/line_color1"
                android:dividerHeight="@dimen/_1dp"/>
            <LinearLayout
                android:gravity="center"
                android:orientation="vertical"
                android:id="@+id/layout_nodata"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_nodata"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="차단된 기록이 없습니다"/>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</LinearLayout>
