<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/settingFrame"
    android:background="@drawable/search_back"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:divider="@color/Gray_divider"
        android:showDividers="middle">
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <FrameLayout
                android:padding="@dimen/_16dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:padding="@dimen/_10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/today_call_request"/>
            </FrameLayout>
            <Switch
                android:id="@+id/swhShowTodayCall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"/>
        </LinearLayout>
        <View
            android:id="@+id/divider1"
            android:background="@color/line_color1"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <FrameLayout
                android:padding="@dimen/_16dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:padding="@dimen/_10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="통화시 팝업창 보기"/>
            </FrameLayout>
            <Switch
                android:id="@+id/swhShowPopupRemain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"/>
        </LinearLayout>
        <View
            android:id="@+id/divider2"
            android:background="@color/line_color1"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:padding="@dimen/_16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="@dimen/_16sp"
                android:textStyle="bold"
                android:textColor="@color/normal_text_color"
                android:padding="@dimen/_10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/popup_position"/>
            <LinearLayout
                android:orientation="vertical"
                android:paddingTop="@dimen/_5dp"
                android:paddingBottom="@dimen/_5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/_16dp"
                android:paddingEnd="@dimen/_16dp">
                <RadioButton
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:id="@+id/chkTop"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="상단"/>
                <RadioButton
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:id="@+id/chkMiddle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="중단"/>
                <RadioButton
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:id="@+id/chkBottom"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="하단"/>
            </LinearLayout>
        </LinearLayout>
        <View
            android:id="@+id/divider3"
            android:background="@color/line_color1"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
    </LinearLayout>
</FrameLayout>
