<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/search_back"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:divider="@color/Gray_divider"
        android:showDividers="middle">
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:background="@drawable/shape_back_lightblue_stroke"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_50dp"
            android:paddingStart="@dimen/_20dp"
            android:paddingEnd="@dimen/_20dp">
            <ImageView
                android:layout_width="21dp"
                android:layout_height="22dp"
                app:srcCompat="@drawable/icon_phone1"/>
            <TextView
                android:textSize="@dimen/_20sp"
                android:textColor="@color/color_sync0"
                android:layout_width="346dp"
                android:layout_height="wrap_content"
                android:text="전화번호 차단"
                android:layout_weight="1"
                android:paddingStart="@dimen/_10dp"/>
        </LinearLayout>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <FrameLayout
                android:padding="@dimen/_16dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="모르는 번호 차단"
                    android:paddingStart="@dimen/_8dp"/>
            </FrameLayout>
            <Switch
                android:id="@+id/swhBlockUnknownNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"/>
        </LinearLayout>
        <View
            android:id="@+id/divider"
            android:background="@color/line_color1"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <FrameLayout
                android:id="@+id/btnBlockTodayCall"
                android:padding="@dimen/_16dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="오늘 전화문의 차단"
                    android:paddingStart="@dimen/_8dp"/>
            </FrameLayout>
            <Switch
                android:id="@+id/swhBlockTodayCall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"/>
        </LinearLayout>
        <View
            android:id="@+id/divider0"
            android:background="@color/line_color1"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <FrameLayout
                android:id="@+id/btnBlockSpecNumber"
                android:padding="@dimen/_16dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="지정 번호 차단"
                    android:paddingStart="@dimen/_8dp"/>
            </FrameLayout>
            <Switch
                android:id="@+id/swhBlockSpecNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"/>
        </LinearLayout>
        <View
            android:id="@+id/divider1"
            android:background="@color/line_color1"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <FrameLayout
                android:id="@+id/btnBlockPrefNumber"
                android:padding="@dimen/_16dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="시작 번호 차단"
                    android:paddingStart="@dimen/_8dp"/>
            </FrameLayout>
            <Switch
                android:id="@+id/swhBlockPrefixNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"/>
        </LinearLayout>
        <View
            android:id="@+id/divider_2"
            android:background="@color/line_color1"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <FrameLayout
                android:padding="@dimen/_16dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:id="@+id/divider2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="모든 번호 차단"
                    android:paddingStart="@dimen/_8dp"/>
            </FrameLayout>
            <Switch
                android:id="@+id/swhBlockAllNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"/>
        </LinearLayout>
        <View
            android:id="@+id/divider3"
            android:background="@color/line_color1"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <FrameLayout
                android:id="@+id/btnCallExplosion"
                android:padding="@dimen/_16dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <TextView
                    android:textSize="@dimen/_16sp"
                    android:textColor="@color/normal_text_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="콜폭 메모 차단"
                    android:paddingStart="@dimen/_8dp"/>
            </FrameLayout>
            <Switch
                android:id="@+id/swhCallExplosion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"/>
        </LinearLayout>
        <View
            android:id="@+id/divider4"
            android:background="@color/line_color1"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
    </LinearLayout>
</FrameLayout>
