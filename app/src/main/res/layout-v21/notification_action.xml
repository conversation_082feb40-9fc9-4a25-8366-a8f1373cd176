<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:id="@+id/action_container"
    android:layout_width="0dp"
    android:layout_height="48dp"
    android:layout_weight="1"
    android:paddingStart="4dp"
    style="@style/Widget.Compat.NotificationActionContainer">
    <ImageView
        android:layout_gravity="start|center_vertical"
        android:id="@+id/action_image"
        android:layout_width="@dimen/notification_action_icon_size"
        android:layout_height="@dimen/notification_action_icon_size"
        android:scaleType="centerInside"/>
    <TextView
        android:ellipsize="end"
        android:layout_gravity="start|center_vertical"
        android:id="@+id/action_text"
        android:clickable="false"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:paddingStart="4dp"
        style="@style/Widget.Compat.NotificationActionText"/>
</LinearLayout>
