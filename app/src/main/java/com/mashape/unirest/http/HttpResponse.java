package com.mashape.unirest.http;

import com.mashape.relocation.Header;
import com.mashape.relocation.HttpEntity;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;

/* loaded from: classes.dex */
public class HttpResponse<T> {
    private T body;
    private int code;
    private Map<String, String> headers;
    private InputStream rawBody;

    private boolean isGzipped() {
        for (Map.Entry<String, String> entry : this.headers.entrySet()) {
            if (entry.getKey().equalsIgnoreCase("content-encoding") && entry.getValue() != null && entry.getValue().equalsIgnoreCase("gzip")) {
                return true;
            }
        }
        return false;
    }

    public HttpResponse(com.mashape.relocation.HttpResponse httpResponse, Class<T> cls) throws Exception {
        HttpEntity entity = httpResponse.getEntity();
        Header[] allHeaders = httpResponse.getAllHeaders();
        this.headers = new HashMap();
        for (Header header : allHeaders) {
            this.headers.put(header.getName().toLowerCase(), header.getValue());
        }
        this.code = httpResponse.getStatusLine().getStatusCode();
        if (entity != null) {
            try {
                try {
                    byte[] bytes = getBytes(isGzipped() ? new GZIPInputStream(entity.getContent()) : entity.getContent());
                    this.rawBody = new ByteArrayInputStream(bytes);
                    if (JsonNode.class.equals(cls)) {
                        this.body = (T) new JsonNode(new String(bytes).trim());
                    } else if (String.class.equals(cls)) {
                        this.body = (T) new String(bytes);
                    } else {
                        if (InputStream.class.equals(cls)) {
                            this.body = (T) this.rawBody;
                            return;
                        }
                        throw new Exception("Unknown result type. Only String, JsonNode and InputStream are supported.");
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            } catch (Exception e2) {
                throw new RuntimeException(e2);
            }
        }
    }

    private static byte[] getBytes(InputStream inputStream) throws IOException {
        if (inputStream instanceof ByteArrayInputStream) {
            int iAvailable = inputStream.available();
            byte[] bArr = new byte[iAvailable];
            inputStream.read(bArr, 0, iAvailable);
            return bArr;
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bArr2 = new byte[1024];
        while (true) {
            int i = inputStream.read(bArr2, 0, 1024);
            if (i != -1) {
                byteArrayOutputStream.write(bArr2, 0, i);
            } else {
                return byteArrayOutputStream.toByteArray();
            }
        }
    }

    public int getCode() {
        return this.code;
    }

    public Map<String, String> getHeaders() {
        return this.headers;
    }

    public InputStream getRawBody() {
        return this.rawBody;
    }

    public T getBody() {
        return this.body;
    }
}
