package org.json;

import java.util.HashMap;

/* loaded from: classes.dex */
public class XMLTokener extends J<PERSON>NTokener {
    public static final HashMap entity = new HashMap(8);

    static {
        entity.put("amp", XML.AMP);
        entity.put("apos", XML.APOS);
        entity.put("gt", XML.GT);
        entity.put("lt", XML.LT);
        entity.put("quot", XML.QUOT);
    }

    public XMLTokener(String str) {
        super(str);
    }

    /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
    public String nextCDATA() throws JSONException {
        StringBuffer stringBuffer = new StringBuffer();
        while (true) {
            char next = next();
            if (end()) {
                throw syntaxError("Unclosed CDATA");
            }
            stringBuffer.append(next);
            int length = stringBuffer.length() - 3;
            if (length >= 0 && stringBuffer.charAt(length) == ']' && stringBuffer.charAt(length + 1) == ']' && stringBuffer.charAt(length + 2) == '>') {
                stringBuffer.setLength(length);
                return stringBuffer.toString();
            }
        }
    }

    public Object nextContent() throws JSONException {
        char next;
        do {
            next = next();
        } while (Character.isWhitespace(next));
        if (next == 0) {
            return null;
        }
        if (next == '<') {
            return XML.LT;
        }
        StringBuffer stringBuffer = new StringBuffer();
        while (next != '<' && next != 0) {
            if (next == '&') {
                stringBuffer.append(nextEntity(next));
            } else {
                stringBuffer.append(next);
            }
            next = next();
        }
        back();
        return stringBuffer.toString().trim();
    }

    public Object nextEntity(char c) throws JSONException {
        char next;
        StringBuffer stringBuffer = new StringBuffer();
        while (true) {
            next = next();
            if (!Character.isLetterOrDigit(next) && next != '#') {
                break;
            }
            stringBuffer.append(Character.toLowerCase(next));
        }
        if (next != ';') {
            StringBuffer stringBuffer2 = new StringBuffer();
            stringBuffer2.append("Missing ';' in XML entity: &");
            stringBuffer2.append((Object) stringBuffer);
            throw syntaxError(stringBuffer2.toString());
        }
        String string = stringBuffer.toString();
        Object obj = entity.get(string);
        if (obj != null) {
            return obj;
        }
        StringBuffer stringBuffer3 = new StringBuffer();
        stringBuffer3.append(c);
        stringBuffer3.append(string);
        stringBuffer3.append(";");
        return stringBuffer3.toString();
    }

    /* JADX WARN: Code restructure failed: missing block: B:88:0x003a, code lost:
    
        back();
     */
    /* JADX WARN: Code restructure failed: missing block: B:89:0x003f, code lost:
    
        return java.lang.Boolean.TRUE;
     */
    /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public Object nextMeta() throws JSONException {
        char next;
        char next2;
        do {
            next = next();
        } while (Character.isWhitespace(next));
        if (next == 0) {
            throw syntaxError("Misshaped meta tag");
        }
        if (next != '\'') {
            if (next == '/') {
                return XML.SLASH;
            }
            if (next == '!') {
                return XML.BANG;
            }
            if (next != '\"') {
                switch (next) {
                    case '<':
                        return XML.LT;
                    case '=':
                        return XML.EQ;
                    case '>':
                        return XML.GT;
                    case '?':
                        return XML.QUEST;
                }
                while (true) {
                    char next3 = next();
                    if (Character.isWhitespace(next3)) {
                        return Boolean.TRUE;
                    }
                    if (next3 != 0 && next3 != '\'' && next3 != '/' && next3 != '!' && next3 != '\"') {
                        switch (next3) {
                        }
                    }
                }
            }
        }
        do {
            next2 = next();
            if (next2 == 0) {
                throw syntaxError("Unterminated string");
            }
        } while (next2 != next);
        return Boolean.TRUE;
    }

    /* JADX WARN: Code restructure failed: missing block: B:113:0x004c, code lost:
    
        back();
     */
    /* JADX WARN: Code restructure failed: missing block: B:114:0x0053, code lost:
    
        return r5.toString();
     */
    /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public Object nextToken() throws JSONException {
        char next;
        do {
            next = next();
        } while (Character.isWhitespace(next));
        if (next == 0) {
            throw syntaxError("Misshaped element");
        }
        if (next != '\'') {
            if (next == '/') {
                return XML.SLASH;
            }
            if (next == '!') {
                return XML.BANG;
            }
            if (next != '\"') {
                switch (next) {
                    case '<':
                        throw syntaxError("Misplaced '<'");
                    case '=':
                        return XML.EQ;
                    case '>':
                        return XML.GT;
                    case '?':
                        return XML.QUEST;
                    default:
                        StringBuffer stringBuffer = new StringBuffer();
                        while (true) {
                            stringBuffer.append(next);
                            next = next();
                            if (Character.isWhitespace(next)) {
                                return stringBuffer.toString();
                            }
                            if (next == 0) {
                                return stringBuffer.toString();
                            }
                            if (next != '\'') {
                                if (next == '/' || next == '[' || next == ']' || next == '!') {
                                    break;
                                } else if (next != '\"') {
                                    switch (next) {
                                    }
                                }
                            }
                        }
                        throw syntaxError("Bad character in a name");
                }
            }
        }
        StringBuffer stringBuffer2 = new StringBuffer();
        while (true) {
            char next2 = next();
            if (next2 == 0) {
                throw syntaxError("Unterminated string");
            }
            if (next2 == next) {
                return stringBuffer2.toString();
            }
            if (next2 == '&') {
                stringBuffer2.append(nextEntity(next2));
            } else {
                stringBuffer2.append(next2);
            }
        }
    }

    public boolean skipPast(String str) throws JSONException {
        boolean z;
        int length = str.length();
        char[] cArr = new char[length];
        for (int i = 0; i < length; i++) {
            char next = next();
            if (next == 0) {
                return false;
            }
            cArr[i] = next;
        }
        int i2 = 0;
        while (true) {
            int i3 = i2;
            int i4 = 0;
            while (true) {
                if (i4 >= length) {
                    z = true;
                    break;
                }
                if (cArr[i3] != str.charAt(i4)) {
                    z = false;
                    break;
                }
                i3++;
                if (i3 >= length) {
                    i3 -= length;
                }
                i4++;
            }
            if (z) {
                return true;
            }
            char next2 = next();
            if (next2 == 0) {
                return false;
            }
            cArr[i2] = next2;
            i2++;
            if (i2 >= length) {
                i2 -= length;
            }
        }
    }
}
