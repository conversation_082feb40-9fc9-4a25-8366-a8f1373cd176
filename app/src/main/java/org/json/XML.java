package org.json;

import com.developer.faker.BuildConfig;
import java.util.Iterator;

/* loaded from: classes.dex */
public class XML {
    public static final Character AMP = new Character('&');
    public static final Character APOS = new Character('\'');
    public static final Character BANG = new Character('!');
    public static final Character EQ = new Character('=');
    public static final Character GT = new Character('>');
    public static final Character LT = new Character('<');
    public static final Character QUEST = new Character('?');
    public static final Character QUOT = new Character('\"');
    public static final Character SLASH = new Character('/');

    public static String escape(String str) {
        StringBuffer stringBuffer = new StringBuffer();
        int length = str.length();
        for (int i = 0; i < length; i++) {
            char cCharAt = str.charAt(i);
            if (cCharAt == '\"') {
                stringBuffer.append("&quot;");
            } else if (cCharAt == '<') {
                stringBuffer.append("&lt;");
            } else if (cCharAt == '>') {
                stringBuffer.append("&gt;");
            } else if (cCharAt == '&') {
                stringBuffer.append("&amp;");
            } else if (cCharAt == '\'') {
                stringBuffer.append("&apos;");
            } else {
                stringBuffer.append(cCharAt);
            }
        }
        return stringBuffer.toString();
    }

    /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
    public static void noSpace(String str) throws JSONException {
        int length = str.length();
        if (length == 0) {
            throw new JSONException("Empty string.");
        }
        for (int i = 0; i < length; i++) {
            if (Character.isWhitespace(str.charAt(i))) {
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("'");
                stringBuffer.append(str);
                stringBuffer.append("' contains a space character.");
                throw new JSONException(stringBuffer.toString());
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:64:0x00ef, code lost:
    
        r7 = r10.nextToken();
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x00f5, code lost:
    
        if ((r7 instanceof java.lang.String) == false) goto L120;
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x0107, code lost:
    
        throw r10.syntaxError("Missing value");
     */
    /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static boolean parse(XMLTokener xMLTokener, JSONObject jSONObject, String str) throws JSONException {
        String str2;
        Object objNextToken;
        Object objNextToken2 = xMLTokener.nextToken();
        int i = 1;
        if (objNextToken2 == BANG) {
            char next = xMLTokener.next();
            if (next == '-') {
                if (xMLTokener.next() == '-') {
                    xMLTokener.skipPast("-->");
                    return false;
                }
                xMLTokener.back();
            } else if (next == '[') {
                if ("CDATA".equals(xMLTokener.nextToken()) && xMLTokener.next() == '[') {
                    String strNextCDATA = xMLTokener.nextCDATA();
                    if (strNextCDATA.length() > 0) {
                        jSONObject.accumulate("content", strNextCDATA);
                    }
                    return false;
                }
                throw xMLTokener.syntaxError("Expected 'CDATA['");
            }
            do {
                Object objNextMeta = xMLTokener.nextMeta();
                if (objNextMeta == null) {
                    throw xMLTokener.syntaxError("Missing '>' after '<!'.");
                }
                if (objNextMeta == LT) {
                    i++;
                } else if (objNextMeta == GT) {
                    i--;
                }
            } while (i > 0);
            return false;
        }
        if (objNextToken2 == QUEST) {
            xMLTokener.skipPast("?>");
            return false;
        }
        if (objNextToken2 == SLASH) {
            Object objNextToken3 = xMLTokener.nextToken();
            if (str == null) {
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("Mismatched close tag ");
                stringBuffer.append(objNextToken3);
                throw xMLTokener.syntaxError(stringBuffer.toString());
            }
            if (!objNextToken3.equals(str)) {
                StringBuffer stringBuffer2 = new StringBuffer();
                stringBuffer2.append("Mismatched ");
                stringBuffer2.append(str);
                stringBuffer2.append(" and ");
                stringBuffer2.append(objNextToken3);
                throw xMLTokener.syntaxError(stringBuffer2.toString());
            }
            if (xMLTokener.nextToken() == GT) {
                return true;
            }
            throw xMLTokener.syntaxError("Misshaped close tag");
        }
        if (objNextToken2 instanceof Character) {
            throw xMLTokener.syntaxError("Misshaped tag");
        }
        String str3 = (String) objNextToken2;
        JSONObject jSONObject2 = new JSONObject();
        while (true) {
            Object objNextToken4 = null;
            while (true) {
                if (objNextToken4 == null) {
                    objNextToken4 = xMLTokener.nextToken();
                }
                if (objNextToken4 instanceof String) {
                    str2 = (String) objNextToken4;
                    Object objNextToken5 = xMLTokener.nextToken();
                    if (objNextToken5 == EQ) {
                        break;
                    }
                    jSONObject2.accumulate(str2, BuildConfig.FLAVOR);
                    objNextToken4 = objNextToken5;
                } else {
                    if (objNextToken4 == SLASH) {
                        if (xMLTokener.nextToken() != GT) {
                            throw xMLTokener.syntaxError("Misshaped tag");
                        }
                        if (jSONObject2.length() > 0) {
                            jSONObject.accumulate(str3, jSONObject2);
                        } else {
                            jSONObject.accumulate(str3, BuildConfig.FLAVOR);
                        }
                        return false;
                    }
                    if (objNextToken4 != GT) {
                        throw xMLTokener.syntaxError("Misshaped tag");
                    }
                    while (true) {
                        Object objNextContent = xMLTokener.nextContent();
                        if (objNextContent == null) {
                            if (str3 == null) {
                                return false;
                            }
                            StringBuffer stringBuffer3 = new StringBuffer();
                            stringBuffer3.append("Unclosed tag ");
                            stringBuffer3.append(str3);
                            throw xMLTokener.syntaxError(stringBuffer3.toString());
                        }
                        if (objNextContent instanceof String) {
                            String str4 = (String) objNextContent;
                            if (str4.length() > 0) {
                                jSONObject2.accumulate("content", stringToValue(str4));
                            }
                        } else if (objNextContent == LT && parse(xMLTokener, jSONObject2, str3)) {
                            if (jSONObject2.length() == 0) {
                                jSONObject.accumulate(str3, BuildConfig.FLAVOR);
                            } else if (jSONObject2.length() == 1 && jSONObject2.opt("content") != null) {
                                jSONObject.accumulate(str3, jSONObject2.opt("content"));
                            } else {
                                jSONObject.accumulate(str3, jSONObject2);
                            }
                            return false;
                        }
                    }
                }
            }
            jSONObject2.accumulate(str2, stringToValue((String) objNextToken));
        }
    }

    public static Object stringToValue(String str) {
        if (BuildConfig.FLAVOR.equals(str)) {
            return str;
        }
        if ("true".equalsIgnoreCase(str)) {
            return Boolean.TRUE;
        }
        if ("false".equalsIgnoreCase(str)) {
            return Boolean.FALSE;
        }
        if ("null".equalsIgnoreCase(str)) {
            return JSONObject.NULL;
        }
        boolean z = false;
        if ("0".equals(str)) {
            return new Integer(0);
        }
        try {
            char cCharAt = str.charAt(0);
            if (cCharAt == '-') {
                cCharAt = str.charAt(1);
                z = true;
            }
            if (cCharAt == '0') {
                if (str.charAt(z ? 2 : 1) == '0') {
                    return str;
                }
            }
            if (cCharAt >= '0' && cCharAt <= '9') {
                if (str.indexOf(46) >= 0) {
                    return Double.valueOf(str);
                }
                if (str.indexOf(101) < 0 && str.indexOf(69) < 0) {
                    Long l = new Long(str);
                    return l.longValue() == ((long) l.intValue()) ? new Integer(l.intValue()) : l;
                }
            }
        } catch (Exception unused) {
        }
        return str;
    }

    public static JSONObject toJSONObject(String str) throws JSONException {
        JSONObject jSONObject = new JSONObject();
        XMLTokener xMLTokener = new XMLTokener(str);
        while (xMLTokener.more() && xMLTokener.skipPast("<")) {
            parse(xMLTokener, jSONObject, null);
        }
        return jSONObject;
    }

    public static String toString(Object obj) throws JSONException {
        return toString(obj, null);
    }

    public static String toString(Object obj, String str) throws JSONException {
        StringBuffer stringBuffer = new StringBuffer();
        if (obj instanceof JSONObject) {
            if (str != null) {
                stringBuffer.append('<');
                stringBuffer.append(str);
                stringBuffer.append('>');
            }
            JSONObject jSONObject = (JSONObject) obj;
            Iterator itKeys = jSONObject.keys();
            while (itKeys.hasNext()) {
                String string = itKeys.next().toString();
                Object objOpt = jSONObject.opt(string);
                if (objOpt == null) {
                    objOpt = BuildConfig.FLAVOR;
                }
                if (objOpt instanceof String) {
                }
                if ("content".equals(string)) {
                    if (objOpt instanceof JSONArray) {
                        JSONArray jSONArray = (JSONArray) objOpt;
                        int length = jSONArray.length();
                        for (int i = 0; i < length; i++) {
                            if (i > 0) {
                                stringBuffer.append('\n');
                            }
                            stringBuffer.append(escape(jSONArray.get(i).toString()));
                        }
                    } else {
                        stringBuffer.append(escape(objOpt.toString()));
                    }
                } else if (objOpt instanceof JSONArray) {
                    JSONArray jSONArray2 = (JSONArray) objOpt;
                    int length2 = jSONArray2.length();
                    for (int i2 = 0; i2 < length2; i2++) {
                        Object obj2 = jSONArray2.get(i2);
                        if (obj2 instanceof JSONArray) {
                            stringBuffer.append('<');
                            stringBuffer.append(string);
                            stringBuffer.append('>');
                            stringBuffer.append(toString(obj2));
                            stringBuffer.append("</");
                            stringBuffer.append(string);
                            stringBuffer.append('>');
                        } else {
                            stringBuffer.append(toString(obj2, string));
                        }
                    }
                } else if (BuildConfig.FLAVOR.equals(objOpt)) {
                    stringBuffer.append('<');
                    stringBuffer.append(string);
                    stringBuffer.append("/>");
                } else {
                    stringBuffer.append(toString(objOpt, string));
                }
            }
            if (str != null) {
                stringBuffer.append("</");
                stringBuffer.append(str);
                stringBuffer.append('>');
            }
            return stringBuffer.toString();
        }
        if (obj.getClass().isArray()) {
            obj = new JSONArray(obj);
        }
        if (obj instanceof JSONArray) {
            JSONArray jSONArray3 = (JSONArray) obj;
            int length3 = jSONArray3.length();
            for (int i3 = 0; i3 < length3; i3++) {
                stringBuffer.append(toString(jSONArray3.opt(i3), str == null ? "array" : str));
            }
            return stringBuffer.toString();
        }
        String strEscape = obj == null ? "null" : escape(obj.toString());
        if (str == null) {
            StringBuffer stringBuffer2 = new StringBuffer();
            stringBuffer2.append("\"");
            stringBuffer2.append(strEscape);
            stringBuffer2.append("\"");
            return stringBuffer2.toString();
        }
        if (strEscape.length() == 0) {
            StringBuffer stringBuffer3 = new StringBuffer();
            stringBuffer3.append("<");
            stringBuffer3.append(str);
            stringBuffer3.append("/>");
            return stringBuffer3.toString();
        }
        StringBuffer stringBuffer4 = new StringBuffer();
        stringBuffer4.append("<");
        stringBuffer4.append(str);
        stringBuffer4.append(">");
        stringBuffer4.append(strEscape);
        stringBuffer4.append("</");
        stringBuffer4.append(str);
        stringBuffer4.append(">");
        return stringBuffer4.toString();
    }
}
