package org.json.zip;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.Kim;

/* loaded from: classes.dex */
public class Compressor extends J<PERSON>Nzip {
    final BitWriter bitwriter;

    private static int bcd(char c) {
        if (c >= '0' && c <= '9') {
            return c - '0';
        }
        if (c == '+') {
            return 12;
        }
        if (c != '-') {
            return c != '.' ? 13 : 10;
        }
        return 11;
    }

    public Compressor(BitWriter bitWriter) {
        this.bitwriter = bitWriter;
    }

    public void flush() throws JSONException {
        pad(8);
    }

    private void one() throws JSONException {
        write(1, 1);
    }

    public void pad(int i) throws JSONException {
        try {
            this.bitwriter.pad(i);
        } catch (Throwable th) {
            throw new JSONException(th);
        }
    }

    private void write(int i, int i2) throws JSONException {
        try {
            this.bitwriter.write(i, i2);
        } catch (Throwable th) {
            throw new JSONException(th);
        }
    }

    private void write(int i, Huff huff) throws JSONException {
        huff.write(i, this.bitwriter);
    }

    private void write(Kim kim, Huff huff) throws JSONException {
        write(kim, 0, kim.length, huff);
    }

    private void write(Kim kim, int i, int i2, Huff huff) throws JSONException {
        while (i < i2) {
            write(kim.get(i), huff);
            i++;
        }
    }

    private void writeAndTick(int i, Keep keep) throws JSONException {
        int iBitsize = keep.bitsize();
        keep.tick(i);
        write(i, iBitsize);
    }

    private void writeArray(JSONArray jSONArray) throws JSONException {
        int length = jSONArray.length();
        if (length == 0) {
            write(1, 3);
            return;
        }
        boolean z = false;
        Object obj = jSONArray.get(0);
        if (obj == null) {
            obj = JSONObject.NULL;
        }
        if (obj instanceof String) {
            write(6, 3);
            writeString((String) obj);
            z = true;
        } else {
            write(7, 3);
            writeValue(obj);
        }
        for (int i = 1; i < length; i++) {
            Object obj2 = jSONArray.get(i);
            if (obj2 == null) {
                obj2 = JSONObject.NULL;
            }
            boolean z2 = obj2 instanceof String;
            if (z2 != z) {
                zero();
            }
            one();
            if (z2) {
                writeString((String) obj2);
            } else {
                writeValue(obj2);
            }
        }
        zero();
        zero();
    }

    private void writeJSON(Object obj) throws JSONException {
        Object jSONArray;
        if (JSONObject.NULL.equals(obj)) {
            write(4, 3);
            return;
        }
        if (Boolean.FALSE.equals(obj)) {
            write(3, 3);
            return;
        }
        if (Boolean.TRUE.equals(obj)) {
            write(2, 3);
            return;
        }
        if (obj instanceof Map) {
            jSONArray = new JSONObject((Map) obj);
        } else if (obj instanceof Collection) {
            jSONArray = new JSONArray((Collection) obj);
        } else {
            jSONArray = obj.getClass().isArray() ? new JSONArray(obj) : obj;
        }
        if (jSONArray instanceof JSONObject) {
            writeObject((JSONObject) jSONArray);
        } else {
            if (jSONArray instanceof JSONArray) {
                writeArray((JSONArray) jSONArray);
                return;
            }
            throw new JSONException("Unrecognized object");
        }
    }

    private void writeName(String str) throws JSONException {
        Kim kim = new Kim(str);
        int iFind = this.namekeep.find(kim);
        if (iFind != -1) {
            one();
            writeAndTick(iFind, this.namekeep);
        } else {
            zero();
            write(kim, this.namehuff);
            write(256, this.namehuff);
            this.namekeep.register(kim);
        }
    }

    private void writeObject(JSONObject jSONObject) throws JSONException {
        Iterator itKeys = jSONObject.keys();
        boolean z = true;
        while (itKeys.hasNext()) {
            Object next = itKeys.next();
            if (next instanceof String) {
                if (z) {
                    write(5, 3);
                    z = false;
                } else {
                    one();
                }
                String str = (String) next;
                writeName(str);
                Object obj = jSONObject.get(str);
                if (obj instanceof String) {
                    zero();
                    writeString((String) obj);
                } else {
                    one();
                    writeValue(obj);
                }
            }
        }
        if (z) {
            write(0, 3);
        } else {
            zero();
        }
    }

    private void writeString(String str) throws JSONException {
        if (str.length() == 0) {
            zero();
            zero();
            write(256, this.substringhuff);
            zero();
            return;
        }
        Kim kim = new Kim(str);
        int iFind = this.stringkeep.find(kim);
        if (iFind != -1) {
            one();
            writeAndTick(iFind, this.stringkeep);
        } else {
            writeSubstring(kim);
            this.stringkeep.register(kim);
        }
    }

    private void writeSubstring(Kim kim) throws JSONException {
        this.substringkeep.reserve();
        zero();
        int i = kim.length;
        int i2 = i - 3;
        int length = 0;
        int i3 = -1;
        int i4 = 0;
        while (true) {
            int i5 = length;
            int iMatch = -1;
            while (i5 <= i2) {
                iMatch = this.substringkeep.match(kim, i5, i);
                if (iMatch != -1) {
                    break;
                } else {
                    i5++;
                }
            }
            if (iMatch == -1) {
                break;
            }
            if (length != i5) {
                zero();
                write(kim, length, i5, this.substringhuff);
                write(256, this.substringhuff);
                if (i3 != -1) {
                    this.substringkeep.registerOne(kim, i3, i4);
                    i3 = -1;
                }
            }
            one();
            writeAndTick(iMatch, this.substringkeep);
            length = this.substringkeep.length(iMatch) + i5;
            if (i3 != -1) {
                this.substringkeep.registerOne(kim, i3, i4);
            }
            i4 = length + 1;
            i3 = i5;
        }
        zero();
        if (length < i) {
            write(kim, length, i, this.substringhuff);
            if (i3 != -1) {
                this.substringkeep.registerOne(kim, i3, i4);
            }
        }
        write(256, this.substringhuff);
        zero();
        this.substringkeep.registerMany(kim);
    }

    private void writeValue(Object obj) throws JSONException {
        if (obj instanceof Number) {
            Number number = (Number) obj;
            String strNumberToString = JSONObject.numberToString(number);
            int iFind = this.values.find(strNumberToString);
            if (iFind != -1) {
                write(2, 2);
                writeAndTick(iFind, this.values);
                return;
            }
            if ((obj instanceof Integer) || (obj instanceof Long)) {
                long jLongValue = number.longValue();
                if (jLongValue >= 0 && jLongValue < 16384) {
                    write(0, 2);
                    if (jLongValue < 16) {
                        zero();
                        write((int) jLongValue, 4);
                        return;
                    }
                    one();
                    if (jLongValue < 128) {
                        zero();
                        write((int) jLongValue, 7);
                        return;
                    } else {
                        one();
                        write((int) jLongValue, 14);
                        return;
                    }
                }
            }
            write(1, 2);
            for (int i = 0; i < strNumberToString.length(); i++) {
                write(bcd(strNumberToString.charAt(i)), 4);
            }
            write(endOfNumber, 4);
            this.values.register(strNumberToString);
            return;
        }
        write(3, 2);
        writeJSON(obj);
    }

    private void zero() throws JSONException {
        write(0, 1);
    }

    public void zip(JSONObject jSONObject) throws JSONException {
        begin();
        writeJSON(jSONObject);
    }

    public void zip(JSONArray jSONArray) throws JSONException {
        begin();
        writeJSON(jSONArray);
    }
}
