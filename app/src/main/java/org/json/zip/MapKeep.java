package org.json.zip;

import java.util.HashMap;
import org.json.Kim;

/* loaded from: classes.dex */
class Map<PERSON>eep extends Keep {
    private Object[] list;
    private HashMap map;

    public MapKeep(int i) {
        super(i);
        this.list = new Object[this.capacity];
        this.map = new HashMap(this.capacity);
    }

    private void compact() {
        int i = 0;
        for (int i2 = 0; i2 < this.capacity; i2++) {
            Object obj = this.list[i2];
            long jAge = age(this.uses[i2]);
            if (jAge > 0) {
                this.uses[i] = jAge;
                this.list[i] = obj;
                this.map.put(obj, new Integer(i));
                i++;
            } else {
                this.map.remove(obj);
            }
        }
        if (i < this.capacity) {
            this.length = i;
        } else {
            this.map.clear();
            this.length = 0;
        }
        this.power = 0;
    }

    public int find(Object obj) {
        Object obj2 = this.map.get(obj);
        if (obj2 instanceof Integer) {
            return ((Integer) obj2).intValue();
        }
        return -1;
    }

    @Override // org.json.zip.PostMortem
    public boolean postMortem(PostMortem postMortem) {
        boolean zEquals;
        MapKeep mapKeep = (MapKeep) postMortem;
        if (this.length != mapKeep.length) {
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(this.length);
            stringBuffer.append(" <> ");
            stringBuffer.append(mapKeep.length);
            JSONzip.log(stringBuffer.toString());
            return false;
        }
        for (int i = 0; i < this.length; i++) {
            Object[] objArr = this.list;
            if (objArr[i] instanceof Kim) {
                zEquals = ((Kim) objArr[i]).equals(mapKeep.list[i]);
            } else {
                Object string = objArr[i];
                Object string2 = mapKeep.list[i];
                if (string instanceof Number) {
                    string = string.toString();
                }
                if (string2 instanceof Number) {
                    string2 = string2.toString();
                }
                zEquals = string.equals(string2);
            }
            if (!zEquals) {
                StringBuffer stringBuffer2 = new StringBuffer();
                stringBuffer2.append("\n[");
                stringBuffer2.append(i);
                stringBuffer2.append("]\n ");
                stringBuffer2.append(this.list[i]);
                stringBuffer2.append("\n ");
                stringBuffer2.append(mapKeep.list[i]);
                stringBuffer2.append("\n ");
                stringBuffer2.append(this.uses[i]);
                stringBuffer2.append("\n ");
                stringBuffer2.append(mapKeep.uses[i]);
                JSONzip.log(stringBuffer2.toString());
                return false;
            }
        }
        return true;
    }

    public void register(Object obj) {
        if (this.length >= this.capacity) {
            compact();
        }
        this.list[this.length] = obj;
        this.map.put(obj, new Integer(this.length));
        this.uses[this.length] = 1;
        this.length++;
    }

    @Override // org.json.zip.Keep
    public Object value(int i) {
        return this.list[i];
    }
}
