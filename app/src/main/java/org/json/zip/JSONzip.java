package org.json.zip;

import android.support.v4.view.InputDeviceCompat;

/* loaded from: classes.dex */
public abstract class JSONzip implements None, PostMortem {
    public static final int end = 256;
    public static final long int14 = 16384;
    public static final long int4 = 16;
    public static final long int7 = 128;
    public static final int maxSubstringLength = 10;
    public static final int minSubstringLength = 3;
    public static final boolean probe = false;
    public static final int substringLimit = 40;
    public static final int zipArrayString = 6;
    public static final int zipArrayValue = 7;
    public static final int zipEmptyArray = 1;
    public static final int zipEmptyObject = 0;
    public static final int zipFalse = 3;
    public static final int zipNull = 4;
    public static final int zipObject = 5;
    public static final int zipTrue = 2;
    protected final Huff namehuff = new Huff(InputDeviceCompat.SOURCE_KEYBOARD);
    protected final Map<PERSON>eep namekeep = new Map<PERSON>eep(9);
    protected final MapKeep stringkeep = new MapKeep(11);
    protected final Huff substringhuff = new Huff(InputDeviceCompat.SOURCE_KEYBOARD);
    protected final TrieKeep substringkeep = new TrieKeep(12);
    protected final MapKeep values = new MapKeep(10);
    public static final int[] twos = {1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 65536};
    public static final byte[] bcd = {48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 46, 45, 43, 69};
    public static final int endOfNumber = bcd.length;

    protected JSONzip() {
        this.namehuff.tick(32, 125);
        this.namehuff.tick(97, 122);
        this.namehuff.tick(256);
        this.namehuff.tick(256);
        this.substringhuff.tick(32, 125);
        this.substringhuff.tick(97, 122);
        this.substringhuff.tick(256);
        this.substringhuff.tick(256);
    }

    protected void begin() {
        this.namehuff.generate();
        this.substringhuff.generate();
    }

    static void log() {
        log("\n");
    }

    static void log(int i) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(i);
        stringBuffer.append(" ");
        log(stringBuffer.toString());
    }

    static void log(int i, int i2) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(i);
        stringBuffer.append(":");
        stringBuffer.append(i2);
        stringBuffer.append(" ");
        log(stringBuffer.toString());
    }

    static void log(String str) {
        System.out.print(str);
    }

    static void logchar(int i, int i2) {
        if (i > 32 && i <= 125) {
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("'");
            stringBuffer.append((char) i);
            stringBuffer.append("':");
            stringBuffer.append(i2);
            stringBuffer.append(" ");
            log(stringBuffer.toString());
            return;
        }
        log(i, i2);
    }

    @Override // org.json.zip.PostMortem
    public boolean postMortem(PostMortem postMortem) {
        JSONzip jSONzip = (JSONzip) postMortem;
        return this.namehuff.postMortem(jSONzip.namehuff) && this.namekeep.postMortem(jSONzip.namekeep) && this.stringkeep.postMortem(jSONzip.stringkeep) && this.substringhuff.postMortem(jSONzip.substringhuff) && this.substringkeep.postMortem(jSONzip.substringkeep) && this.values.postMortem(jSONzip.values);
    }
}
