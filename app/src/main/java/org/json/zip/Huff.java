package org.json.zip;

import org.json.JSONException;

/* loaded from: classes.dex */
public class Huff implements None, PostMortem {
    private final int domain;
    private final Symbol[] symbols;
    private Symbol table;
    private boolean upToDate = false;
    private int width;

    private static class Symbol implements PostMortem {
        public final int integer;
        public long weight = 0;
        public Symbol next = null;
        public Symbol back = null;
        public Symbol one = null;
        public Symbol zero = null;

        public Symbol(int i) {
            this.integer = i;
        }

        @Override // org.json.zip.PostMortem
        public boolean postMortem(PostMortem postMortem) {
            Symbol symbol = (Symbol) postMortem;
            if (this.integer != symbol.integer || this.weight != symbol.weight) {
                return false;
            }
            boolean zPostMortem = true;
            if ((this.back != null) != (symbol.back != null)) {
                return false;
            }
            Symbol symbol2 = this.zero;
            Symbol symbol3 = this.one;
            if (symbol2 == null) {
                if (symbol.zero != null) {
                    return false;
                }
            } else {
                zPostMortem = symbol2.postMortem(symbol.zero);
            }
            if (symbol3 == null) {
                if (symbol.one != null) {
                    return false;
                }
                return zPostMortem;
            }
            return symbol3.postMortem(symbol.one);
        }
    }

    public Huff(int i) {
        this.domain = i;
        int i2 = (i * 2) - 1;
        this.symbols = new Symbol[i2];
        for (int i3 = 0; i3 < i; i3++) {
            this.symbols[i3] = new Symbol(i3);
        }
        while (i < i2) {
            this.symbols[i] = new Symbol(-1);
            i++;
        }
    }

    public void generate() {
        int i;
        Symbol symbol;
        Symbol symbol2;
        if (this.upToDate) {
            return;
        }
        Symbol symbol3 = this.symbols[0];
        this.table = null;
        symbol3.next = null;
        Symbol symbol4 = symbol3;
        Symbol symbol5 = symbol4;
        int i2 = 1;
        while (true) {
            i = this.domain;
            if (i2 >= i) {
                break;
            }
            Symbol symbol6 = this.symbols[i2];
            if (symbol6.weight < symbol4.weight) {
                symbol6.next = symbol4;
                symbol4 = symbol6;
            } else {
                if (symbol6.weight < symbol5.weight) {
                    symbol5 = symbol4;
                }
                while (true) {
                    symbol2 = symbol5.next;
                    if (symbol2 == null || symbol6.weight < symbol2.weight) {
                        break;
                    } else {
                        symbol5 = symbol2;
                    }
                }
                symbol6.next = symbol2;
                symbol5.next = symbol6;
                symbol5 = symbol6;
            }
            i2++;
        }
        Symbol symbol7 = symbol4;
        while (true) {
            Symbol symbol8 = symbol4.next;
            Symbol symbol9 = symbol8.next;
            Symbol symbol10 = this.symbols[i];
            i++;
            symbol10.weight = symbol4.weight + symbol8.weight;
            symbol10.zero = symbol4;
            symbol10.one = symbol8;
            symbol10.back = null;
            symbol4.back = symbol10;
            symbol8.back = symbol10;
            if (symbol9 == null) {
                this.table = symbol10;
                this.upToDate = true;
                return;
            }
            if (symbol10.weight < symbol9.weight) {
                symbol10.next = symbol9;
                symbol7 = symbol10;
                symbol4 = symbol7;
            } else {
                while (true) {
                    symbol = symbol7.next;
                    if (symbol == null || symbol10.weight < symbol.weight) {
                        break;
                    } else {
                        symbol7 = symbol;
                    }
                }
                symbol10.next = symbol;
                symbol7.next = symbol10;
                symbol4 = symbol9;
                symbol7 = symbol10;
            }
        }
    }

    private boolean postMortem(int i) {
        int[] iArr = new int[this.domain];
        Symbol symbol = this.symbols[i];
        if (symbol.integer != i) {
            return false;
        }
        int i2 = 0;
        while (true) {
            Symbol symbol2 = symbol.back;
            if (symbol2 != null) {
                if (symbol2.zero == symbol) {
                    iArr[i2] = 0;
                } else {
                    if (symbol2.one != symbol) {
                        return false;
                    }
                    iArr[i2] = 1;
                }
                i2++;
                symbol = symbol2;
            } else {
                Symbol symbol3 = this.table;
                if (symbol != symbol3) {
                    return false;
                }
                this.width = 0;
                while (symbol3.integer == -1) {
                    i2--;
                    symbol3 = iArr[i2] != 0 ? symbol3.one : symbol3.zero;
                }
                return symbol3.integer == i && i2 == 0;
            }
        }
    }

    @Override // org.json.zip.PostMortem
    public boolean postMortem(PostMortem postMortem) {
        for (int i = 0; i < this.domain; i++) {
            if (!postMortem(i)) {
                JSONzip.log("\nBad huff ");
                JSONzip.logchar(i, i);
                return false;
            }
        }
        return this.table.postMortem(((Huff) postMortem).table);
    }

    /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
    public int read(BitReader bitReader) throws JSONException {
        try {
            this.width = 0;
            Symbol symbol = this.table;
            while (symbol.integer == -1) {
                this.width++;
                symbol = bitReader.bit() ? symbol.one : symbol.zero;
            }
            tick(symbol.integer);
            return symbol.integer;
        } catch (Throwable th) {
            throw new JSONException(th);
        }
    }

    public void tick(int i) {
        this.symbols[i].weight++;
        this.upToDate = false;
    }

    public void tick(int i, int i2) {
        while (i <= i2) {
            tick(i);
            i++;
        }
    }

    private void write(Symbol symbol, BitWriter bitWriter) throws JSONException {
        try {
            Symbol symbol2 = symbol.back;
            if (symbol2 != null) {
                this.width++;
                write(symbol2, bitWriter);
                if (symbol2.zero == symbol) {
                    bitWriter.zero();
                } else {
                    bitWriter.one();
                }
            }
        } catch (Throwable th) {
            throw new JSONException(th);
        }
    }

    public void write(int i, BitWriter bitWriter) throws JSONException {
        this.width = 0;
        write(this.symbols[i], bitWriter);
        tick(i);
    }
}
