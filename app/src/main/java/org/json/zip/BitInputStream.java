package org.json.zip;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: classes.dex */
public class BitInputStream implements BitReader {
    static final int[] mask = {0, 1, 3, 7, 15, 31, 63, 127, 255};
    private int available;
    private InputStream in;
    private long nrBits;
    private int unread;

    public BitInputStream(InputStream inputStream) {
        this.available = 0;
        this.unread = 0;
        this.nrBits = 0L;
        this.in = inputStream;
    }

    public BitInputStream(InputStream inputStream, int i) {
        this.available = 0;
        this.unread = 0;
        this.nrBits = 0L;
        this.in = inputStream;
        this.unread = i;
        this.available = 8;
    }

    @Override // org.json.zip.BitReader
    public boolean bit() throws IOException {
        return read(1) != 0;
    }

    @Override // org.json.zip.BitReader
    public long nrBits() {
        return this.nrBits;
    }

    @Override // org.json.zip.BitReader
    public boolean pad(int i) throws IOException {
        int i2 = i - ((int) (this.nrBits % i));
        boolean z = true;
        for (int i3 = 0; i3 < i2; i3++) {
            if (bit()) {
                z = false;
            }
        }
        return z;
    }

    /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
    @Override // org.json.zip.BitReader
    public int read(int i) throws IOException {
        int i2 = 0;
        if (i == 0) {
            return 0;
        }
        if (i < 0 || i > 32) {
            throw new IOException("Bad read width.");
        }
        while (i > 0) {
            if (this.available == 0) {
                this.unread = this.in.read();
                if (this.unread < 0) {
                    throw new IOException("Attempt to read past end.");
                }
                this.available = 8;
            }
            int i3 = this.available;
            if (i <= i3) {
                i3 = i;
            }
            int i4 = this.unread;
            int i5 = this.available;
            i -= i3;
            i2 |= ((i4 >>> (i5 - i3)) & mask[i3]) << i;
            this.nrBits += i3;
            this.available = i5 - i3;
        }
        return i2;
    }
}
