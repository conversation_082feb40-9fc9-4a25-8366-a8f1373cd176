package org.json.zip;

/* loaded from: classes.dex */
abstract class Keep implements None, PostMortem {
    protected int capacity;
    protected int length = 0;
    protected int power = 0;
    protected long[] uses;

    public abstract Object value(int i);

    public Keep(int i) {
        this.capacity = JSONzip.twos[i];
        this.uses = new long[this.capacity];
    }

    public static long age(long j) {
        if (j >= 32) {
            return 16L;
        }
        return j / 2;
    }

    public int bitsize() {
        while (true) {
            int[] iArr = JSONzip.twos;
            int i = this.power;
            if (iArr[i] >= this.length) {
                return i;
            }
            this.power = i + 1;
        }
    }

    public void tick(int i) {
        long[] jArr = this.uses;
        jArr[i] = jArr[i] + 1;
    }
}
