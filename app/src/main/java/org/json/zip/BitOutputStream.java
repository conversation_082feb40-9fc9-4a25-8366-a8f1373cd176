package org.json.zip;

import java.io.IOException;
import java.io.OutputStream;

/* loaded from: classes.dex */
public class BitOutputStream implements BitWriter {
    private OutputStream out;
    private int unwritten;
    private long nrBits = 0;
    private int vacant = 8;

    public BitOutputStream(OutputStream outputStream) {
        this.out = outputStream;
    }

    @Override // org.json.zip.BitWriter
    public long nrBits() {
        return this.nrBits;
    }

    @Override // org.json.zip.BitWriter
    public void one() throws IOException {
        write(1, 1);
    }

    @Override // org.json.zip.BitWriter
    public void pad(int i) throws IOException {
        int i2 = i - ((int) (this.nrBits % i));
        int i3 = i2 & 7;
        if (i3 > 0) {
            write(0, i3);
            i2 -= i3;
        }
        while (i2 > 0) {
            write(0, 8);
            i2 -= 8;
        }
        this.out.flush();
    }

    /* JADX WARN: Unreachable blocks removed: 1, instructions: 1 */
    @Override // org.json.zip.BitWriter
    public void write(int i, int i2) throws IOException {
        if (i == 0 && i2 == 0) {
            return;
        }
        if (i2 <= 0 || i2 > 32) {
            throw new IOException("Bad write width.");
        }
        while (i2 > 0) {
            int i3 = this.vacant;
            if (i2 <= i3) {
                i3 = i2;
            }
            int i4 = this.unwritten;
            i2 -= i3;
            int i5 = (i >>> i2) & BitInputStream.mask[i3];
            int i6 = this.vacant;
            this.unwritten = i4 | (i5 << (i6 - i3));
            this.nrBits += i3;
            this.vacant = i6 - i3;
            if (this.vacant == 0) {
                this.out.write(this.unwritten);
                this.unwritten = 0;
                this.vacant = 8;
            }
        }
    }

    @Override // org.json.zip.BitWriter
    public void zero() throws IOException {
        write(0, 1);
    }
}
