package org.json;

import java.util.Arrays;

/* loaded from: classes.dex */
public class Kim {
    private byte[] bytes;
    private int hashcode;
    public int length;
    private String string;

    public Kim(byte[] bArr, int i, int i2) {
        this.bytes = null;
        this.hashcode = 0;
        this.length = 0;
        this.string = null;
        this.hashcode = 0;
        this.length = i2 - i;
        int i3 = this.length;
        if (i3 > 0) {
            this.bytes = new byte[i3];
            int i4 = 1;
            for (int i5 = 0; i5 < this.length; i5++) {
                int i6 = bArr[i5 + i] & 255;
                i4 += i6;
                this.hashcode += i4;
                this.bytes[i5] = (byte) i6;
            }
            this.hashcode += i4 << 16;
        }
    }

    public Kim(byte[] bArr, int i) {
        this(bArr, 0, i);
    }

    public Kim(Kim kim, int i, int i2) {
        this(kim.bytes, i, i2);
    }

    public Kim(String str) throws JSONException {
        int i;
        this.bytes = null;
        int i2 = 0;
        this.hashcode = 0;
        this.length = 0;
        this.string = null;
        int length = str.length();
        this.hashcode = 0;
        this.length = 0;
        if (length > 0) {
            int i3 = 0;
            while (i3 < length) {
                char cCharAt = str.charAt(i3);
                if (cCharAt <= 127) {
                    this.length++;
                } else if (cCharAt <= 16383) {
                    this.length += 2;
                } else {
                    if (cCharAt >= 55296 && cCharAt <= 57343) {
                        i3++;
                        char cCharAt2 = str.charAt(i3);
                        if (cCharAt > 56319 || cCharAt2 < 56320 || cCharAt2 > 57343) {
                            throw new JSONException("Bad UTF16");
                        }
                    }
                    this.length += 3;
                }
                i3++;
            }
            this.bytes = new byte[this.length];
            int i4 = 1;
            int i5 = 0;
            while (i2 < length) {
                int iCharAt = str.charAt(i2);
                if (iCharAt <= 127) {
                    this.bytes[i5] = (byte) iCharAt;
                    i4 += iCharAt;
                    this.hashcode += i4;
                    i5++;
                } else {
                    if (iCharAt <= 16383) {
                        int i6 = (iCharAt >>> 7) | 128;
                        byte[] bArr = this.bytes;
                        bArr[i5] = (byte) i6;
                        int i7 = i4 + i6;
                        this.hashcode += i7;
                        i = i5 + 1;
                        int i8 = iCharAt & 127;
                        bArr[i] = (byte) i8;
                        i4 = i7 + i8;
                        this.hashcode += i4;
                    } else {
                        if (iCharAt >= 55296 && iCharAt <= 56319) {
                            i2++;
                            iCharAt = (((iCharAt & 1023) << 10) | (str.charAt(i2) & 1023)) + 65536;
                        }
                        int i9 = (iCharAt >>> 14) | 128;
                        byte[] bArr2 = this.bytes;
                        bArr2[i5] = (byte) i9;
                        int i10 = i4 + i9;
                        this.hashcode += i10;
                        int i11 = i5 + 1;
                        int i12 = ((iCharAt >>> 7) & 255) | 128;
                        bArr2[i11] = (byte) i12;
                        int i13 = i10 + i12;
                        this.hashcode += i13;
                        i = i11 + 1;
                        int i14 = iCharAt & 127;
                        bArr2[i] = (byte) i14;
                        i4 = i13 + i14;
                        this.hashcode += i4;
                    }
                    i5 = i + 1;
                }
                i2++;
            }
            this.hashcode += i4 << 16;
        }
    }

    public int characterAt(int i) throws JSONException {
        int i2 = get(i);
        if ((i2 & 128) == 0) {
            return i2;
        }
        int i3 = get(i + 1);
        if ((i3 & 128) == 0) {
            int i4 = ((i2 & 127) << 7) | i3;
            if (i4 > 127) {
                return i4;
            }
        } else {
            int i5 = get(i + 2);
            int i6 = ((i2 & 127) << 14) | ((i3 & 127) << 7) | i5;
            if ((i5 & 128) == 0 && i6 > 16383 && i6 <= 1114111 && (i6 < 55296 || i6 > 57343)) {
                return i6;
            }
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("Bad character at ");
        stringBuffer.append(i);
        throw new JSONException(stringBuffer.toString());
    }

    public static int characterSize(int i) throws JSONException {
        if (i >= 0 && i <= 1114111) {
            if (i <= 127) {
                return 1;
            }
            return i <= 16383 ? 2 : 3;
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("Bad character ");
        stringBuffer.append(i);
        throw new JSONException(stringBuffer.toString());
    }

    public int copy(byte[] bArr, int i) {
        System.arraycopy(this.bytes, 0, bArr, i, this.length);
        return i + this.length;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof Kim)) {
            return false;
        }
        Kim kim = (Kim) obj;
        if (this == kim) {
            return true;
        }
        if (this.hashcode != kim.hashcode) {
            return false;
        }
        return Arrays.equals(this.bytes, kim.bytes);
    }

    public int get(int i) throws JSONException {
        if (i < 0 || i > this.length) {
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("Bad character at ");
            stringBuffer.append(i);
            throw new JSONException(stringBuffer.toString());
        }
        return this.bytes[i] & 255;
    }

    public int hashCode() {
        return this.hashcode;
    }

    public String toString() throws JSONException {
        if (this.string == null) {
            char[] cArr = new char[this.length];
            int iCharacterSize = 0;
            int i = 0;
            while (iCharacterSize < this.length) {
                int iCharacterAt = characterAt(iCharacterSize);
                if (iCharacterAt < 65536) {
                    cArr[i] = (char) iCharacterAt;
                } else {
                    cArr[i] = (char) (((iCharacterAt - 65536) >>> 10) | 55296);
                    i++;
                    cArr[i] = (char) (56320 | (iCharacterAt & 1023));
                }
                i++;
                iCharacterSize += characterSize(iCharacterAt);
            }
            this.string = new String(cArr, 0, i);
        }
        return this.string;
    }
}
