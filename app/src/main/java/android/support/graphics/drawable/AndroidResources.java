package android.support.graphics.drawable;

import android.support.annotation.StyleableRes;

/* loaded from: classes.dex */
class AndroidResources {
    public static final int FAST_OUT_LINEAR_IN = 17563663;
    public static final int FAST_OUT_SLOW_IN = 17563661;
    public static final int LINEAR_OUT_SLOW_IN = 17563662;
    static final int STYLEABLE_ANIMATED_VECTOR_DRAWABLE_DRAWABLE = 0;

    @StyleableRes
    static final int STYLEABLE_ANIMATED_VECTOR_DRAWABLE_TARGET_ANIMATION = 1;

    @StyleableRes
    static final int STYLEABLE_ANIMATED_VECTOR_DRAWABLE_TARGET_NAME = 0;
    public static final int STYLEABLE_ANIMATOR_DURATION = 1;
    public static final int STYLEABLE_ANIMATOR_INTERPOLATOR = 0;
    public static final int STYLEABLE_ANIMATOR_REPEAT_COUNT = 3;
    public static final int STYLEABLE_ANIMATOR_REPEAT_MODE = 4;
    public static final int STYLEABLE_ANIMATOR_SET_ORDERING = 0;
    public static final int STYLEABLE_ANIMATOR_START_OFFSET = 2;
    public static final int STYLEABLE_ANIMATOR_VALUE_FROM = 5;
    public static final int STYLEABLE_ANIMATOR_VALUE_TO = 6;
    public static final int STYLEABLE_ANIMATOR_VALUE_TYPE = 7;
    public static final int STYLEABLE_KEYFRAME_FRACTION = 3;
    public static final int STYLEABLE_KEYFRAME_INTERPOLATOR = 1;
    public static final int STYLEABLE_KEYFRAME_VALUE = 0;
    public static final int STYLEABLE_KEYFRAME_VALUE_TYPE = 2;
    public static final int STYLEABLE_PATH_INTERPOLATOR_CONTROL_X_1 = 0;
    public static final int STYLEABLE_PATH_INTERPOLATOR_CONTROL_X_2 = 2;
    public static final int STYLEABLE_PATH_INTERPOLATOR_CONTROL_Y_1 = 1;
    public static final int STYLEABLE_PATH_INTERPOLATOR_CONTROL_Y_2 = 3;
    public static final int STYLEABLE_PATH_INTERPOLATOR_PATH_DATA = 4;
    public static final int STYLEABLE_PROPERTY_ANIMATOR_PATH_DATA = 1;
    public static final int STYLEABLE_PROPERTY_ANIMATOR_PROPERTY_NAME = 0;
    public static final int STYLEABLE_PROPERTY_ANIMATOR_PROPERTY_X_NAME = 2;
    public static final int STYLEABLE_PROPERTY_ANIMATOR_PROPERTY_Y_NAME = 3;
    public static final int STYLEABLE_PROPERTY_VALUES_HOLDER_PROPERTY_NAME = 3;
    public static final int STYLEABLE_PROPERTY_VALUES_HOLDER_VALUE_FROM = 0;
    public static final int STYLEABLE_PROPERTY_VALUES_HOLDER_VALUE_TO = 1;
    public static final int STYLEABLE_PROPERTY_VALUES_HOLDER_VALUE_TYPE = 2;
    static final int STYLEABLE_VECTOR_DRAWABLE_ALPHA = 4;
    static final int STYLEABLE_VECTOR_DRAWABLE_AUTO_MIRRORED = 5;
    static final int STYLEABLE_VECTOR_DRAWABLE_CLIP_PATH_NAME = 0;
    static final int STYLEABLE_VECTOR_DRAWABLE_CLIP_PATH_PATH_DATA = 1;
    static final int STYLEABLE_VECTOR_DRAWABLE_GROUP_NAME = 0;
    static final int STYLEABLE_VECTOR_DRAWABLE_GROUP_PIVOT_X = 1;
    static final int STYLEABLE_VECTOR_DRAWABLE_GROUP_PIVOT_Y = 2;
    static final int STYLEABLE_VECTOR_DRAWABLE_GROUP_ROTATION = 5;
    static final int STYLEABLE_VECTOR_DRAWABLE_GROUP_SCALE_X = 3;
    static final int STYLEABLE_VECTOR_DRAWABLE_GROUP_SCALE_Y = 4;
    static final int STYLEABLE_VECTOR_DRAWABLE_GROUP_TRANSLATE_X = 6;
    static final int STYLEABLE_VECTOR_DRAWABLE_GROUP_TRANSLATE_Y = 7;
    static final int STYLEABLE_VECTOR_DRAWABLE_HEIGHT = 2;
    static final int STYLEABLE_VECTOR_DRAWABLE_NAME = 0;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_FILL_ALPHA = 12;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_FILL_COLOR = 1;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_NAME = 0;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_PATH_DATA = 2;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_ALPHA = 11;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_COLOR = 3;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_LINE_CAP = 8;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_LINE_JOIN = 9;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_MITER_LIMIT = 10;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_WIDTH = 4;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_TRIM_PATH_END = 6;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_TRIM_PATH_FILLTYPE = 13;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_TRIM_PATH_OFFSET = 7;
    static final int STYLEABLE_VECTOR_DRAWABLE_PATH_TRIM_PATH_START = 5;
    static final int STYLEABLE_VECTOR_DRAWABLE_TINT = 1;
    static final int STYLEABLE_VECTOR_DRAWABLE_TINT_MODE = 6;
    static final int STYLEABLE_VECTOR_DRAWABLE_VIEWPORT_HEIGHT = 8;
    static final int STYLEABLE_VECTOR_DRAWABLE_VIEWPORT_WIDTH = 7;
    static final int STYLEABLE_VECTOR_DRAWABLE_WIDTH = 3;
    static final int[] STYLEABLE_VECTOR_DRAWABLE_TYPE_ARRAY = {android.R.attr.name, android.R.attr.tint, android.R.attr.height, android.R.attr.width, android.R.attr.alpha, android.R.attr.autoMirrored, android.R.attr.tintMode, android.R.attr.viewportWidth, android.R.attr.viewportHeight};
    static final int[] STYLEABLE_VECTOR_DRAWABLE_GROUP = {android.R.attr.name, android.R.attr.pivotX, android.R.attr.pivotY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.translateX, android.R.attr.translateY};
    static final int[] STYLEABLE_VECTOR_DRAWABLE_PATH = {android.R.attr.name, android.R.attr.fillColor, android.R.attr.pathData, android.R.attr.strokeColor, android.R.attr.strokeWidth, android.R.attr.trimPathStart, android.R.attr.trimPathEnd, android.R.attr.trimPathOffset, android.R.attr.strokeLineCap, android.R.attr.strokeLineJoin, android.R.attr.strokeMiterLimit, android.R.attr.strokeAlpha, android.R.attr.fillAlpha, android.R.attr.fillType};
    static final int[] STYLEABLE_VECTOR_DRAWABLE_CLIP_PATH = {android.R.attr.name, android.R.attr.pathData};
    static final int[] STYLEABLE_ANIMATED_VECTOR_DRAWABLE = {android.R.attr.drawable};
    static final int[] STYLEABLE_ANIMATED_VECTOR_DRAWABLE_TARGET = {android.R.attr.name, android.R.attr.animation};
    public static final int[] STYLEABLE_ANIMATOR = {android.R.attr.interpolator, android.R.attr.duration, android.R.attr.startOffset, android.R.attr.repeatCount, android.R.attr.repeatMode, android.R.attr.valueFrom, android.R.attr.valueTo, android.R.attr.valueType};
    public static final int[] STYLEABLE_ANIMATOR_SET = {android.R.attr.ordering};
    public static final int[] STYLEABLE_PROPERTY_VALUES_HOLDER = {android.R.attr.valueFrom, android.R.attr.valueTo, android.R.attr.valueType, android.R.attr.propertyName};
    public static final int[] STYLEABLE_KEYFRAME = {android.R.attr.value, android.R.attr.interpolator, android.R.attr.valueType, android.R.attr.fraction};
    public static final int[] STYLEABLE_PROPERTY_ANIMATOR = {android.R.attr.propertyName, android.R.attr.pathData, android.R.attr.propertyXName, android.R.attr.propertyYName};
    public static final int[] STYLEABLE_PATH_INTERPOLATOR = {android.R.attr.controlX1, android.R.attr.controlY1, android.R.attr.controlX2, android.R.attr.controlY2, android.R.attr.pathData};

    private AndroidResources() {
    }
}
