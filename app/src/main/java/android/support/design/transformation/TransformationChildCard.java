package android.support.design.transformation;

import android.content.Context;
import android.support.design.circularreveal.cardview.CircularRevealCardView;
import android.util.AttributeSet;

/* loaded from: classes.dex */
public class TransformationChildCard extends CircularRevealCardView {
    public TransformationChildCard(Context context) {
        this(context, null);
    }

    public TransformationChildCard(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }
}
