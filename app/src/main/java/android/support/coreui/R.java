package android.support.coreui;

/* loaded from: classes.dex */
public final class R {
    private R() {
    }

    public static final class attr {
        public static final int alpha = 0x7f040027;
        public static final int coordinatorLayoutStyle = 0x7f04009e;
        public static final int font = 0x7f0400d0;
        public static final int fontProviderAuthority = 0x7f0400d2;
        public static final int fontProviderCerts = 0x7f0400d3;
        public static final int fontProviderFetchStrategy = 0x7f0400d4;
        public static final int fontProviderFetchTimeout = 0x7f0400d5;
        public static final int fontProviderPackage = 0x7f0400d6;
        public static final int fontProviderQuery = 0x7f0400d7;
        public static final int fontStyle = 0x7f0400d8;
        public static final int fontVariationSettings = 0x7f0400d9;
        public static final int fontWeight = 0x7f0400da;
        public static final int keylines = 0x7f040106;
        public static final int layout_anchor = 0x7f04010b;
        public static final int layout_anchorGravity = 0x7f04010c;
        public static final int layout_behavior = 0x7f04010d;
        public static final int layout_dodgeInsetEdges = 0x7f040110;
        public static final int layout_insetEdge = 0x7f040111;
        public static final int layout_keyline = 0x7f040112;
        public static final int statusBarBackground = 0x7f04016d;
        public static final int ttcIndex = 0x7f0401cf;

        private attr() {
        }
    }

    public static final class color {
        public static final int notification_action_color_filter = 0x7f06008a;
        public static final int notification_icon_bg_color = 0x7f06008b;
        public static final int ripple_material_light = 0x7f060097;
        public static final int secondary_text_default_material_light = 0x7f060099;

        private color() {
        }
    }

    public static final class dimen {
        public static final int compat_button_inset_horizontal_material = 0x7f070082;
        public static final int compat_button_inset_vertical_material = 0x7f070083;
        public static final int compat_button_padding_horizontal_material = 0x7f070084;
        public static final int compat_button_padding_vertical_material = 0x7f070085;
        public static final int compat_control_corner_material = 0x7f070086;
        public static final int compat_notification_large_icon_max_height = 0x7f070087;
        public static final int compat_notification_large_icon_max_width = 0x7f070088;
        public static final int notification_action_icon_size = 0x7f0700f7;
        public static final int notification_action_text_size = 0x7f0700f8;
        public static final int notification_big_circle_margin = 0x7f0700f9;
        public static final int notification_content_margin_start = 0x7f0700fa;
        public static final int notification_large_icon_height = 0x7f0700fb;
        public static final int notification_large_icon_width = 0x7f0700fc;
        public static final int notification_main_column_padding_top = 0x7f0700fd;
        public static final int notification_media_narrow_margin = 0x7f0700fe;
        public static final int notification_right_icon_size = 0x7f0700ff;
        public static final int notification_right_side_padding_top = 0x7f070100;
        public static final int notification_small_icon_background_padding = 0x7f070101;
        public static final int notification_small_icon_size_as_large = 0x7f070102;
        public static final int notification_subtext_size = 0x7f070103;
        public static final int notification_top_pad = 0x7f070104;
        public static final int notification_top_pad_large_text = 0x7f070105;

        private dimen() {
        }
    }

    public static final class drawable {
        public static final int notification_action_background = 0x7f08009c;
        public static final int notification_bg = 0x7f08009d;
        public static final int notification_bg_low = 0x7f08009e;
        public static final int notification_bg_low_normal = 0x7f08009f;
        public static final int notification_bg_low_pressed = 0x7f0800a0;
        public static final int notification_bg_normal = 0x7f0800a1;
        public static final int notification_bg_normal_pressed = 0x7f0800a2;
        public static final int notification_icon_background = 0x7f0800a3;
        public static final int notification_template_icon_bg = 0x7f0800a4;
        public static final int notification_template_icon_low_bg = 0x7f0800a5;
        public static final int notification_tile_bg = 0x7f0800a6;
        public static final int notify_panel_notification_icon_bg = 0x7f0800a7;

        private drawable() {
        }
    }

    public static final class id {
        public static final int action_container = 0x7f09000e;
        public static final int action_divider = 0x7f090010;
        public static final int action_image = 0x7f090011;
        public static final int action_text = 0x7f090017;
        public static final int actions = 0x7f090018;
        public static final int async = 0x7f09001f;
        public static final int blocking = 0x7f090024;
        public static final int bottom = 0x7f090025;
        public static final int chronometer = 0x7f09003f;
        public static final int end = 0x7f090062;
        public static final int forever = 0x7f09006f;
        public static final int icon = 0x7f090075;
        public static final int icon_group = 0x7f090077;
        public static final int info = 0x7f09007e;
        public static final int italic = 0x7f09007f;
        public static final int left = 0x7f090087;
        public static final int line1 = 0x7f090088;
        public static final int line3 = 0x7f090089;
        public static final int none = 0x7f0900a2;
        public static final int normal = 0x7f0900a3;
        public static final int notification_background = 0x7f0900a6;
        public static final int notification_main_column = 0x7f0900a7;
        public static final int notification_main_column_container = 0x7f0900a8;
        public static final int right = 0x7f0900b3;
        public static final int right_icon = 0x7f0900b4;
        public static final int right_side = 0x7f0900b5;
        public static final int start = 0x7f0900e0;
        public static final int tag_transition_group = 0x7f0900f2;
        public static final int tag_unhandled_key_event_manager = 0x7f0900f3;
        public static final int tag_unhandled_key_listeners = 0x7f0900f4;
        public static final int text = 0x7f0900f6;
        public static final int text2 = 0x7f0900f7;
        public static final int time = 0x7f090100;
        public static final int title = 0x7f090101;
        public static final int top = 0x7f090107;

        private id() {
        }
    }

    public static final class integer {
        public static final int status_bar_notification_info_maxnum = 0x7f0a000e;

        private integer() {
        }
    }

    public static final class layout {
        public static final int notification_action = 0x7f0c0042;
        public static final int notification_action_tombstone = 0x7f0c0043;
        public static final int notification_template_custom_big = 0x7f0c004a;
        public static final int notification_template_icon_group = 0x7f0c004b;
        public static final int notification_template_part_chronometer = 0x7f0c004f;
        public static final int notification_template_part_time = 0x7f0c0050;

        private layout() {
        }
    }

    public static final class string {
        public static final int status_bar_notification_info_overflow = 0x7f0f004a;

        private string() {
        }
    }

    public static final class style {
        public static final int TextAppearance_Compat_Notification = 0x7f100118;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f100119;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f10011b;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f10011e;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f100120;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f1001c8;
        public static final int Widget_Compat_NotificationActionText = 0x7f1001c9;
        public static final int Widget_Support_CoordinatorLayout = 0x7f1001f8;

        private style() {
        }
    }

    public static final class styleable {
        public static final int ColorStateListItem_alpha = 0x00000002;
        public static final int ColorStateListItem_android_alpha = 0x00000001;
        public static final int ColorStateListItem_android_color = 0x00000000;
        public static final int CoordinatorLayout_Layout_android_layout_gravity = 0x00000000;
        public static final int CoordinatorLayout_Layout_layout_anchor = 0x00000001;
        public static final int CoordinatorLayout_Layout_layout_anchorGravity = 0x00000002;
        public static final int CoordinatorLayout_Layout_layout_behavior = 0x00000003;
        public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 0x00000004;
        public static final int CoordinatorLayout_Layout_layout_insetEdge = 0x00000005;
        public static final int CoordinatorLayout_Layout_layout_keyline = 0x00000006;
        public static final int CoordinatorLayout_keylines = 0x00000000;
        public static final int CoordinatorLayout_statusBarBackground = 0x00000001;
        public static final int FontFamilyFont_android_font = 0x00000000;
        public static final int FontFamilyFont_android_fontStyle = 0x00000002;
        public static final int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static final int FontFamilyFont_android_fontWeight = 0x00000001;
        public static final int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static final int FontFamilyFont_font = 0x00000005;
        public static final int FontFamilyFont_fontStyle = 0x00000006;
        public static final int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static final int FontFamilyFont_fontWeight = 0x00000008;
        public static final int FontFamilyFont_ttcIndex = 0x00000009;
        public static final int FontFamily_fontProviderAuthority = 0x00000000;
        public static final int FontFamily_fontProviderCerts = 0x00000001;
        public static final int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static final int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static final int FontFamily_fontProviderPackage = 0x00000004;
        public static final int FontFamily_fontProviderQuery = 0x00000005;
        public static final int GradientColorItem_android_color = 0x00000000;
        public static final int GradientColorItem_android_offset = 0x00000001;
        public static final int GradientColor_android_centerColor = 0x00000007;
        public static final int GradientColor_android_centerX = 0x00000003;
        public static final int GradientColor_android_centerY = 0x00000004;
        public static final int GradientColor_android_endColor = 0x00000001;
        public static final int GradientColor_android_endX = 0x0000000a;
        public static final int GradientColor_android_endY = 0x0000000b;
        public static final int GradientColor_android_gradientRadius = 0x00000005;
        public static final int GradientColor_android_startColor = 0x00000000;
        public static final int GradientColor_android_startX = 0x00000008;
        public static final int GradientColor_android_startY = 0x00000009;
        public static final int GradientColor_android_tileMode = 0x00000006;
        public static final int GradientColor_android_type = 0x00000002;
        public static final int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, com.developer.faker.R.attr.alpha};
        public static final int[] CoordinatorLayout = {com.developer.faker.R.attr.keylines, com.developer.faker.R.attr.statusBarBackground};
        public static final int[] CoordinatorLayout_Layout = {android.R.attr.layout_gravity, com.developer.faker.R.attr.layout_anchor, com.developer.faker.R.attr.layout_anchorGravity, com.developer.faker.R.attr.layout_behavior, com.developer.faker.R.attr.layout_dodgeInsetEdges, com.developer.faker.R.attr.layout_insetEdge, com.developer.faker.R.attr.layout_keyline};
        public static final int[] FontFamily = {com.developer.faker.R.attr.fontProviderAuthority, com.developer.faker.R.attr.fontProviderCerts, com.developer.faker.R.attr.fontProviderFetchStrategy, com.developer.faker.R.attr.fontProviderFetchTimeout, com.developer.faker.R.attr.fontProviderPackage, com.developer.faker.R.attr.fontProviderQuery};
        public static final int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, com.developer.faker.R.attr.font, com.developer.faker.R.attr.fontStyle, com.developer.faker.R.attr.fontVariationSettings, com.developer.faker.R.attr.fontWeight, com.developer.faker.R.attr.ttcIndex};
        public static final int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static final int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};

        private styleable() {
        }
    }
}
