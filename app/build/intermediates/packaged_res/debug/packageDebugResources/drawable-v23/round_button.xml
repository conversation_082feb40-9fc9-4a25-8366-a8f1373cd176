<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="false">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#8e7cde"
                android:endColor="#c656a1"
                android:angle="0"
                android:type="linear"/>
            <corners android:radius="@dimen/_40dp"/>
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#8e7cde"
                android:endColor="#b95397"
                android:angle="0"
                android:type="linear"/>
            <corners android:radius="@dimen/_40dp"/>
        </shape>
    </item>
</selector>
