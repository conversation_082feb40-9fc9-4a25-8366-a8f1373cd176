<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <FrameLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="0dp">
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/pop_background"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/titlebar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_100dp">
                <LinearLayout
                    android:orientation="horizontal"
                    android:background="@null"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="207">
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="89"/>
                    <ImageView
                        android:layout_gravity="center"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:src="@drawable/pop_mark"
                        android:adjustViewBounds="true"
                        android:layout_weight="101"/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="18"/>
                </LinearLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="698">
                    <TextView
                        android:textSize="@dimen/_20sp"
                        android:textColor="#434343"
                        android:gravity="bottom|left"
                        android:layout_gravity="center"
                        android:id="@+id/txtPhoneNumber"
                        android:padding="@dimen/_5dp"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:text="메모텍스트입니다."
                        android:layout_weight="1"/>
                    <TextView
                        android:textSize="@dimen/_14sp"
                        android:textColor="#434343"
                        android:gravity="top|left"
                        android:layout_gravity="center"
                        android:id="@+id/txtRemainDay"
                        android:padding="@dimen/_5dp"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:text="제휴일(D-2)"
                        android:layout_weight="1"/>
                    <TextView
                        android:textSize="@dimen/_16sp"
                        android:textColor="@color/red"
                        android:gravity="left"
                        android:layout_gravity="center"
                        android:id="@+id/txtNewNotice"
                        android:padding="@dimen/_5dp"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:text="새로운 공지가 있습니다."
                        android:layout_weight="1"/>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/control"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_50dp">
                <Button
                    android:id="@+id/btnAccept"
                    android:background="@drawable/pop_accept"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"/>
                <Button
                    android:id="@+id/btnReject"
                    android:background="@drawable/pop_reject"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"/>
            </LinearLayout>
            <LinearLayout
                android:orientation="vertical"
                android:background="@null"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <FrameLayout
                    android:id="@+id/viewDetail"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_60dp">
                    <LinearLayout
                        android:orientation="horizontal"
                        android:background="@drawable/pop_back"
                        android:visibility="visible"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">
                        <TextView
                            android:textSize="@dimen/_20sp"
                            android:textColor="@color/white"
                            android:gravity="center"
                            android:layout_gravity="center"
                            android:id="@+id/txtResultCount"
                            android:background="@null"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:text="내역조회(7)"
                            android:layout_weight="442"/>
                        <ImageView
                            android:layout_gravity="center"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:src="@drawable/pop_result"
                            android:adjustViewBounds="true"
                            android:layout_weight="202"/>
                        <TextView
                            android:textSize="@dimen/_20sp"
                            android:textColor="@color/white"
                            android:gravity="center"
                            android:layout_gravity="center"
                            android:id="@+id/txtResultPhoneNumber"
                            android:background="@null"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:text="01012345678"
                            android:layout_weight="442"/>
                    </LinearLayout>
                    <Button
                        android:textSize="@dimen/_20sp"
                        android:textColor="@color/normal_text_color"
                        android:id="@+id/btnDetail"
                        android:background="@drawable/img_back2"
                        android:padding="@dimen/_5dp"
                        android:visibility="visible"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/wait"
                        android:maxLines="1"/>
                </FrameLayout>
                <ScrollView
                    android:id="@+id/scrollView"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:orientation="vertical"
                        android:id="@+id/lstDetail"
                        android:scrollbars="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                </ScrollView>
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
    <Button
        android:layout_gravity="top|right"
        android:id="@+id/btnClose"
        android:background="@drawable/pop_close_gray"
        android:layout_width="@dimen/_40dp"
        android:layout_height="@dimen/_40dp"
        android:layout_margin="@dimen/_5dp"/>
</FrameLayout>
