<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <FrameLayout
        android:id="@+id/viewDetail"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_60dp">
        <LinearLayout
            android:orientation="horizontal"
            android:background="@drawable/img_back2"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <Button
                android:layout_gravity="center_vertical"
                android:background="@drawable/icon2"
                android:visibility="visible"
                android:layout_width="92dp"
                android:layout_height="37dp"/>
            <Button
                android:textSize="@dimen/_20sp"
                android:textColor="@color/white"
                android:id="@+id/notice_button"
                android:background="@null"
                android:layout_width="240dp"
                android:layout_height="match_parent"
                android:text="새로운 공지가 있습니다."
                android:layout_weight="442"/>
            <Button
                android:textSize="@dimen/_16sp"
                android:textColor="#ffffff"
                android:layout_gravity="center_vertical"
                android:id="@+id/notice_close"
                android:background="@drawable/pop_close_gray"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_margin="@dimen/_4dp"
                android:layout_marginLeft="@dimen/_10dp"
                android:layout_marginStart="@dimen/_10dp"/>
        </LinearLayout>
    </FrameLayout>
</FrameLayout>
