<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/img_back2"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/_20sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:text="모르는 번호 차단"/>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="연락처에 등록되지 않은 모든 번호의\10수신전화를 차단합니다.\10(문자는 차단되지 않습니다.)"/>
        <LinearLayout
            android:paddingTop="@dimen/_20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnCancel"
                android:background="@color/color_sync0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_cancel"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnOk"
                android:background="@color/color_sync1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_ok"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
        </LinearLayout>
    </LinearLayout>
    <Button
        android:layout_gravity="end|top"
        android:id="@+id/btnClose"
        android:background="@drawable/pop_close"
        android:layout_width="@dimen/_30dp"
        android:layout_height="@dimen/_30dp"
        android:layout_margin="@dimen/_5dp"/>
</FrameLayout>
